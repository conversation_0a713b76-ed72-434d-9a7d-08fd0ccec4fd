<?php
$pageTitle = 'Animation Test';
include 'includes/header.php';
?>

<style>
    .test-section {
        padding: 100px 0;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .test-card {
        background: white;
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .animation-demo {
        margin: 30px 0;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }
</style>

<!-- Test Section 1: Loader Test -->
<section class="test-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="test-card loading-animation" data-aos="fade-up">
            <h2>PCCS Logistics Animation Test</h2>
            <p>This page tests all the animations and UI enhancements implemented.</p>
            
            <div class="animation-demo">
                <h4>Tracking Steps Animation</h4>
                <div class="tracking-steps">
                    <div class="tracking-step" data-step="pickup">
                        <div class="step-icon">
                            <i class="fa fa-cube"></i>
                        </div>
                        <div class="step-content">
                            <h5>Pickup</h5>
                            <p>Package collected</p>
                        </div>
                    </div>
                    <div class="tracking-step" data-step="track">
                        <div class="step-icon">
                            <i class="fa fa-truck"></i>
                        </div>
                        <div class="step-content">
                            <h5>In Transit</h5>
                            <p>On the way</p>
                        </div>
                    </div>
                    <div class="tracking-step" data-step="drop">
                        <div class="step-icon">
                            <i class="fa fa-map-marker"></i>
                        </div>
                        <div class="step-content">
                            <h5>Out for Delivery</h5>
                            <p>Almost there</p>
                        </div>
                    </div>
                    <div class="tracking-step" data-step="delivery">
                        <div class="step-icon">
                            <i class="fa fa-check"></i>
                        </div>
                        <div class="step-content">
                            <h5>Delivered</h5>
                            <p>Package delivered</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Test Section 2: Counter Animation -->
<section class="test-section" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
    <div class="container">
        <div class="test-card loading-animation" data-aos="fade-left">
            <h3>Statistics Counter Test</h3>
            <div class="hero-stats">
                <div class="stat-item">
                    <div class="stat-number counter" data-target="500">0</div>
                    <div class="stat-label">Happy Clients</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number counter" data-target="1000">0</div>
                    <div class="stat-label">Deliveries</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number counter" data-target="50">0</div>
                    <div class="stat-label">Countries</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Test Section 3: Truck Animation -->
<section class="test-section" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="container">
        <div class="test-card loading-animation" data-aos="zoom-in">
            <h3>Vehicle Animation Test</h3>
            <div class="animation-demo">
                <div class="truck-animation" style="width: 200px; height: 100px; margin: 0 auto; background: url('images/truck.png') no-repeat center; background-size: contain;"></div>
            </div>
            
            <div class="hero-features">
                <div class="feature-item">
                    <i class="fa fa-clock-o"></i>
                    <span>24/7 Service</span>
                </div>
                <div class="feature-item">
                    <i class="fa fa-shield"></i>
                    <span>Insured Cargo</span>
                </div>
                <div class="feature-item">
                    <i class="fa fa-map-marker"></i>
                    <span>GPS Tracking</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Test Section 4: Button Animations -->
<section class="test-section" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
    <div class="container">
        <div class="test-card loading-animation" data-aos="fade-right">
            <h3>Button Animation Test</h3>
            <div class="hero-buttons">
                <a href="#" class="h3-bnr-btn site-button btn-primary">
                    <i class="fa fa-truck"></i> Primary Button
                </a>
                <a href="#" class="h3-bnr-btn site-button btn-outline">
                    <i class="fa fa-phone"></i> Outline Button
                </a>
            </div>
            
            <p>Test the hover effects and animations on these buttons.</p>
        </div>
    </div>
</section>

<script>
// Test script to manually trigger animations
document.addEventListener('DOMContentLoaded', function() {
    console.log('Animation test page loaded');
    
    // Test tracking steps animation after 2 seconds
    setTimeout(() => {
        const trackingSteps = document.querySelector('.tracking-steps');
        if (trackingSteps) {
            console.log('Triggering tracking steps animation');
            // This will be handled by the main animation system
        }
    }, 2000);
});
</script>

<?php include 'includes/footer.php'; ?>
