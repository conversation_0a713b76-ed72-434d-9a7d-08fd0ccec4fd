<?php
require_once '../config/database.php';
require_once '../includes/functions.php';
requireLogin();

$pageTitle = 'Dashboard';

// Get statistics
$stats = [
    'services' => $db->count('services', "status = 'active'"),
    'blog_posts' => $db->count('blog_posts', "status = 'published'"),
    'testimonials' => $db->count('testimonials', "status = 'active'"),
    'contact_messages' => $db->count('contact_messages', "status = 'new'"),
    'shipments' => $db->count('shipments'),
    'subscribers' => $db->count('newsletter_subscribers', "status = 'active'")
];

// Get recent activities
$recentMessages = $db->fetchAll("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
$recentShipments = $db->fetchAll("SELECT * FROM shipments ORDER BY created_at DESC LIMIT 5");
?>

<?php include 'includes/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="small text-white-50">Services</div>
                                    <div class="h4"><?php echo $stats['services']; ?></div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-cogs fa-2x"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a class="small text-white stretched-link" href="services.php">View Details</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                    <div class="card bg-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="small text-white-50">Blog Posts</div>
                                    <div class="h4"><?php echo $stats['blog_posts']; ?></div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-blog fa-2x"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a class="small text-white stretched-link" href="blog.php">View Details</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                    <div class="card bg-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="small text-white-50">Testimonials</div>
                                    <div class="h4"><?php echo $stats['testimonials']; ?></div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a class="small text-white stretched-link" href="testimonials.php">View Details</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                    <div class="card bg-warning text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="small text-white-50">New Messages</div>
                                    <div class="h4"><?php echo $stats['contact_messages']; ?></div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a class="small text-white stretched-link" href="messages.php">View Details</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                    <div class="card bg-danger text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="small text-white-50">Shipments</div>
                                    <div class="h4"><?php echo $stats['shipments']; ?></div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shipping-fast fa-2x"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a class="small text-white stretched-link" href="shipments.php">View Details</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                    <div class="card bg-secondary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="small text-white-50">Subscribers</div>
                                    <div class="h4"><?php echo $stats['subscribers']; ?></div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a class="small text-white stretched-link" href="subscribers.php">View Details</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-envelope me-1"></i>
                            Recent Messages
                        </div>
                        <div class="card-body">
                            <?php if ($recentMessages): ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($recentMessages as $message): ?>
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($message['name']); ?></h6>
                                                <small><?php echo formatDate($message['created_at']); ?></small>
                                            </div>
                                            <p class="mb-1"><?php echo truncateText($message['message'], 80); ?></p>
                                            <small class="text-muted"><?php echo htmlspecialchars($message['email']); ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No recent messages</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-shipping-fast me-1"></i>
                            Recent Shipments
                        </div>
                        <div class="card-body">
                            <?php if ($recentShipments): ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($recentShipments as $shipment): ?>
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($shipment['tracking_number']); ?></h6>
                                                <small><?php echo formatDate($shipment['created_at']); ?></small>
                                            </div>
                                            <p class="mb-1">From: <?php echo htmlspecialchars($shipment['sender_name']); ?></p>
                                            <p class="mb-1">To: <?php echo htmlspecialchars($shipment['receiver_name']); ?></p>
                                            <span class="badge bg-<?php echo $shipment['current_status'] == 'delivered' ? 'success' : 'primary'; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $shipment['current_status'])); ?>
                                            </span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No recent shipments</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>