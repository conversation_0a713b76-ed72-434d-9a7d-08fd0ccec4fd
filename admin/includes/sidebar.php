<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'dashboard' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'services' ? 'active' : ''; ?>" href="services.php">
                    <i class="fas fa-cogs"></i>
                    Services
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo in_array(getCurrentPage(), ['blog', 'blog-add', 'blog-edit']) ? 'active' : ''; ?>" href="blog.php">
                    <i class="fas fa-blog"></i>
                    Blog Posts
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'testimonials' ? 'active' : ''; ?>" href="testimonials.php">
                    <i class="fas fa-star"></i>
                    Testimonials
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'shipments' ? 'active' : ''; ?>" href="shipments.php">
                    <i class="fas fa-shipping-fast"></i>
                    Shipments
                    <?php
                    $newShipments = $db->count('shipments', "current_status = 'pickup'");
                    if ($newShipments > 0):
                    ?>
                        <span class="notification-badge"><?php echo $newShipments; ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'messages' ? 'active' : ''; ?>" href="messages.php">
                    <i class="fas fa-envelope"></i>
                    Messages
                    <?php
                    $newMessages = $db->count('contact_messages', "status = 'new'");
                    if ($newMessages > 0):
                    ?>
                        <span class="notification-badge"><?php echo $newMessages; ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'subscribers' ? 'active' : ''; ?>" href="subscribers.php">
                    <i class="fas fa-users"></i>
                    Subscribers
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'pages' ? 'active' : ''; ?>" href="pages.php">
                    <i class="fas fa-file-alt"></i>
                    Pages
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'gallery' ? 'active' : ''; ?>" href="gallery.php">
                    <i class="fas fa-images"></i>
                    Gallery
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'faqs' ? 'active' : ''; ?>" href="faqs.php">
                    <i class="fas fa-question-circle"></i>
                    FAQs
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Settings</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i>
                    Site Settings
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo getCurrentPage() == 'users' ? 'active' : ''; ?>" href="users.php">
                    <i class="fas fa-user-shield"></i>
                    Admin Users
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="../index.php" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    View Website
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </li>
        </ul>
    </div>
</nav>