<?php
// Include configuration and functions
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get site settings from database
$siteSettings = $db->fetch("SELECT * FROM site_settings WHERE id = 1");
if (!$siteSettings) {
    // Default settings if not found in database
    $siteSettings = [
        'site_name' => SITE_NAME,
        'site_logo' => 'PCCS-LOGISTICS-logo.png',
        'phone' => '2 ************',
        'email' => SITE_EMAIL,
        'address' => 'Your Address Here'
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- META -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="logistics, shipping, transportation, cargo, freight, PCCS" />
    <meta name="author" content="PCCS Logistics" />
    <meta name="robots" content="index, follow" />
    <meta name="description" content="PCCS Logistics - Professional logistics and transportation services" />

    <!-- FAVICONS ICON -->
    <link rel="icon" href="images/favicon.png" type="image/x-icon" />
    <link rel="shortcut icon" type="image/x-icon" href="images/favicon.png" />

    <!-- PAGE TITLE HERE -->
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . $siteSettings['site_name'] : $siteSettings['site_name']; ?></title>

    <!-- MOBILE SPECIFIC -->
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- BOOTSTRAP STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css" />
    <!-- FONTAWESOME STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/font-awesome.min.css" />
    <!-- OWL CAROUSEL STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/owl.carousel.min.css">
    <!-- SLICK CAROUSEL STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/slick.min.css">
    <!-- SLICK CAROUSEL STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/slick-theme.css">
    <!-- MAGNIFIC POPUP STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/magnific-popup.min.css">
    <!-- SWIPER SLIDER STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/swiper-bundle.min.css" />
    <!-- MAIN STYLE SHEET -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    <!-- Lc light box popup -->
    <link rel="stylesheet" href="css/lc_lightbox.css" />
    <!-- Price Range Slider -->
    <link rel="stylesheet" href="css/bootstrap-slider.min.css" />

    <!-- Lenis Smooth Scrolling -->
    <script src="https://cdn.jsdelivr.net/gh/studio-freight/lenis@1.0.19/bundled/lenis.min.js"></script>

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom CSS for animations -->
    <style>
        .logistics-animation {
            position: relative;
            overflow: hidden;
        }

        .truck-animation {
            position: absolute;
            transition: transform 3s ease-in-out;
        }

        .loading-animation {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease-out;
        }

        .loading-animation.animate {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced PCCS Loader Styles */
        .pccs-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
            background-size: 400% 400%;
            animation: gradientShift 4s ease-in-out infinite;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            transition: opacity 0.8s ease;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .pccs-loader::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="truck-pattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M10,25 L40,25 L40,35 L10,35 Z" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23truck-pattern)"/></svg>');
            opacity: 0.1;
            animation: patternMove 10s linear infinite;
        }

        @keyframes patternMove {
            0% { transform: translateX(-50px); }
            100% { transform: translateX(50px); }
        }

        .logo-container {
            position: relative;
            z-index: 2;
        }

        .pccs-loader img {
            max-width: 280px;
            height: auto;
            filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
            animation: logoFloat 3s ease-in-out infinite;
            position: relative;
            z-index: 2;
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
            }
            50% {
                transform: translateY(-20px) scale(1.05);
                filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.4));
            }
        }

        .loading-text {
            color: white;
            text-align: center;
            margin-top: 30px;
            position: relative;
            z-index: 2;
        }

        .loading-text h4 {
            font-size: 24px;
            font-weight: 300;
            margin-bottom: 20px;
            animation: textPulse 2s ease-in-out infinite;
        }

        @keyframes textPulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }

        .loading-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            animation: loadingProgress 2s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .loading-dots {
            display: flex;
            gap: 8px;
            margin-top: 20px;
        }

        .loading-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            animation: dotBounce 1.4s ease-in-out infinite both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }
        .loading-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes dotBounce {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .tracking-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 50px 0;
        }

        .tracking-step {
            text-align: center;
            position: relative;
            flex: 1;
        }

        .tracking-step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #ddd;
            z-index: -1;
        }

        .tracking-step:last-child::after {
            display: none;
        }

        .tracking-step.active::after {
            background: #007bff;
        }

        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            transition: all 0.3s ease;
        }

        .tracking-step.active .step-icon {
            background: #007bff;
            color: white;
        }

        /* Advanced Logistics Animations */
        .truck-animation {
            animation: truckMove 4s ease-in-out infinite alternate;
        }

        @keyframes truckMove {
            0% { transform: translateX(-30px) scale(1); }
            50% { transform: translateX(0) scale(1.05); }
            100% { transform: translateX(30px) scale(1); }
        }

        .loading-animation {
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 1.2s ease-out forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Tracking Steps */
        .tracking-steps::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 10%;
            right: 10%;
            height: 4px;
            background: linear-gradient(90deg, #007bff 0%, #28a745 25%, #ffc107 50%, #dc3545 75%, #6f42c1 100%);
            z-index: 1;
            border-radius: 2px;
            animation: progressLine 3s ease-in-out infinite;
        }

        @keyframes progressLine {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .tracking-step {
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .tracking-step:hover {
            transform: translateY(-10px);
        }

        .step-icon {
            box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
            animation: stepPulse 2s ease-in-out infinite;
        }

        @keyframes stepPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3); }
            50% { transform: scale(1.1); box-shadow: 0 15px 40px rgba(0, 123, 255, 0.5); }
        }

        .tracking-step[data-step="pickup"] .step-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
            animation-delay: 0s;
        }

        .tracking-step[data-step="track"] .step-icon {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            animation-delay: 0.5s;
        }

        .tracking-step[data-step="drop"] .step-icon {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            animation-delay: 1s;
        }

        .tracking-step[data-step="delivery"] .step-icon {
            background: linear-gradient(135deg, #dc3545, #c82333);
            animation-delay: 1.5s;
        }

        /* Truck Movement Animation */
        .trck-animation-wrap {
            position: relative;
            animation: truckDrive 6s linear infinite;
        }

        @keyframes truckDrive {
            0% { transform: translateX(-50px); }
            25% { transform: translateX(20px) rotateY(0deg); }
            50% { transform: translateX(50px) rotateY(0deg); }
            75% { transform: translateX(20px) rotateY(0deg); }
            100% { transform: translateX(-50px); }
        }

        .spin-tyres {
            animation: spin 0.5s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .blink-image {
            animation: blink 1.5s ease-in-out infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* Ship Animation */
        .slide-top {
            animation: shipFloat 4s ease-in-out infinite;
        }

        @keyframes shipFloat {
            0%, 100% { transform: translateY(0px) rotateZ(0deg); }
            25% { transform: translateY(-15px) rotateZ(2deg); }
            50% { transform: translateY(-10px) rotateZ(0deg); }
            75% { transform: translateY(-20px) rotateZ(-2deg); }
        }

        /* Plane Animation */
        .slide-top-fast {
            animation: planefly 3s ease-in-out infinite;
        }

        @keyframes planefly {
            0% { transform: translateX(-30px) translateY(10px) rotateZ(-5deg); }
            50% { transform: translateX(30px) translateY(-10px) rotateZ(5deg); }
            100% { transform: translateX(-30px) translateY(10px) rotateZ(-5deg); }
        }

        /* Cloud Animations */
        .clowd1-move, .clowd2-move, .clowd3-move {
            animation: cloudFloat 8s ease-in-out infinite;
        }

        .clowd2-move {
            animation-delay: -2s;
            animation-duration: 10s;
        }

        .clowd3-move {
            animation-delay: -4s;
            animation-duration: 12s;
        }

        @keyframes cloudFloat {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(20px); }
        }

        /* Enhanced UI/UX Styles */
        .slide-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        .h-banner-wrap {
            position: relative;
            z-index: 2;
        }

        .text-gradient {
            background: linear-gradient(135deg, #007bff, #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 20px;
        }

        .hero-description {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 123, 255, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: white;
            padding: 13px 28px;
            border-radius: 50px;
            transition: all 0.3s ease;
        }

        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: white;
            transform: translateY(-3px);
        }

        .hero-stats {
            display: flex;
            gap: 40px;
            margin-top: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffc107;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .hero-features {
            display: flex;
            gap: 30px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        .feature-item i {
            font-size: 1.2rem;
            color: #ffc107;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .floating-icon {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            animation: floatIcon 3s ease-in-out infinite;
        }

        .floating-icon i {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.8);
        }

        @keyframes floatIcon {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .animate-text {
            animation: textSlideIn 1s ease-out;
        }

        @keyframes textSlideIn {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive Animations */
        @media (max-width: 768px) {
            .tracking-steps {
                flex-direction: column;
                gap: 30px;
            }

            .tracking-steps::before {
                display: none;
            }

            .step-icon {
                width: 50px;
                height: 50px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                flex-direction: column;
                gap: 20px;
            }

            .hero-features {
                flex-direction: column;
                gap: 15px;
            }

            .hero-buttons {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>

<body>
    <!-- Enhanced PCCS Loader -->
    <div class="pccs-loader" id="pccsLoader">
        <div class="logo-container">
            <img src="PCCS-LOGISTICS-logo.png" alt="PCCS Logistics">
        </div>
        <div class="loading-text">
            <h4>PCCS Logistics</h4>
            <div class="loading-progress"></div>
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
        </div>
    </div>

    <!-- LOADING AREA START ===== -->
    <div class="loading-area" style="display: none;">
        <div class="loading-box"></div>
        <div class="loading-pic">
            <div id="outer-barG">
                <div id="front-barG" class="bar-animationG">
                    <div id="barG_1" class="bar-lineG"></div>
                    <div id="barG_2" class="bar-lineG"></div>
                    <div id="barG_3" class="bar-lineG"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- LOADING AREA  END ====== -->

    <div class="page-wraper">
        <!-- HEADER START -->
        <header class="site-header header-style-3 mobile-sider-drawer-menu">
            <div class="sticky-header main-bar-wraper navbar-expand-lg">
                <div class="main-bar">
                    <div class="container-fluid clearfix">
                        <div class="logo-header">
                            <div class="logo-header-inner logo-header-one">
                                <a href="index.php">
                                    <img src="PCCS-LOGISTICS-logo.png" alt="PCCS Logistics" style="max-height: 60px; width: auto;">
                                </a>
                            </div>
                        </div>

                        <!-- NAV Toggle Button -->
                        <button id="mobile-side-drawer" data-target=".header-nav" data-toggle="collapse" type="button"
                            class="navbar-toggler collapsed">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar icon-bar-first"></span>
                            <span class="icon-bar icon-bar-two"></span>
                            <span class="icon-bar icon-bar-three"></span>
                        </button>

                        <!-- MAIN Nav -->
                        <div class="nav-animation header-nav navbar-collapse collapse d-flex justify-content-center">
                            <ul class="nav navbar-nav">
                                <li class="<?php echo isActivePage('index') ? 'active' : ''; ?>">
                                    <a href="index.php">Home</a>
                                </li>
                                <li class="has-child <?php echo in_array(getCurrentPage(), ['about', 'faq']) ? 'active' : ''; ?>">
                                    <a href="javascript:;">Pages</a>
                                    <ul class="sub-menu">
                                        <li><a href="about.php">About Us</a></li>
                                        <li><a href="faq.php">FAQ</a></li>
                                    </ul>
                                </li>
                                <li class="has-child <?php echo in_array(getCurrentPage(), ['services', 'service-detail']) ? 'active' : ''; ?>">
                                    <a href="javascript:;">Services</a>
                                    <ul class="sub-menu">
                                        <li><a href="services.php">Our Services</a></li>
                                        <li><a href="tracking.php">Track Shipment</a></li>
                                    </ul>
                                </li>
                                <li class="has-child <?php echo in_array(getCurrentPage(), ['blog', 'blog-detail']) ? 'active' : ''; ?>">
                                    <a href="javascript:;">Blog</a>
                                    <ul class="sub-menu">
                                        <li><a href="blog.php">Blog</a></li>
                                    </ul>
                                </li>
                                <li class="<?php echo isActivePage('contact') ? 'active' : ''; ?>">
                                    <a href="contact.php">Contact</a>
                                </li>
                            </ul>
                        </div>

                        <!-- Header Right Section-->
                        <div class="extra-nav header-2-nav">
                            <div class="extra-cell">
                                <div class="header-search">
                                    <a href="#search" class="header-search-icon"><i class="fa fa-search"></i></a>
                                </div>
                            </div>
                            <div class="extra-cell">
                                <div class="header-nav-call-section">
                                    <div class="detail">
                                        <span class="title">Call Us Now</span>
                                        <span><a href="tel:<?php echo str_replace(' ', '', $siteSettings['phone']); ?>"><?php echo $siteSettings['phone']; ?></a></span>
                                    </div>
                                    <div class="media">
                                        <img src="images/call.png" alt="Call">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <!-- HEADER END -->