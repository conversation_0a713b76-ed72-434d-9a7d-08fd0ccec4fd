        <!-- FOOTER START -->
        <footer class="site-footer footer-dark">
            <!-- FOOTER BLOCKES START -->
            <div class="footer-top">
                <div class="container">
                    <div class="row">

                        <div class="col-lg-3 col-md-12 col-sm-12 footer-col-4">
                            <div class="widget widget_about">
                                <div class="logo-footer clearfix">
                                    <a href="index.php">
                                        <img src="PCCS-LOGISTICS-logo.png" alt="PCCS Logistics" style="max-width: 180px; height: auto; filter: brightness(0) invert(1);">
                                    </a>
                                </div>
                                <p>Professional logistics solutions worldwide. We provide reliable, efficient, and cost-effective logistics services to meet all your shipping and transportation needs.</p>
                                <ul class="ftr-list">
                                    <li><p><span>Address :</span>123 Logistics Avenue, Business District, City 12345</p></li>
                                    <li><p><span>Email :</span><EMAIL></p></li>
                                    <li><p><span>Phone :</span>+****************</p></li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-12 col-sm-12 footer-col-4">
                            <div class="widget widget_services ftr-list-center">
                                <h3 class="widget-title">Quick Links</h3>
                                <ul>
                                    <li><a href="index.php">Home</a></li>
                                    <li><a href="about.php">About Us</a></li>
                                    <li><a href="services.php">Services</a></li>
                                    <li><a href="tracking.php">Track Shipment</a></li>
                                    <li><a href="contact.php">Contact</a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-12 col-sm-12 footer-col-4">
                            <div class="widget widget_services ftr-list-center">
                                <h3 class="widget-title">Our Services</h3>
                                <ul>
                                    <?php
                                    $services = $db->fetchAll("SELECT title, slug FROM services WHERE status = 'active' LIMIT 5");
                                    if ($services) {
                                        foreach ($services as $service) {
                                            echo '<li><a href="service-detail.php?slug=' . $service['slug'] . '">' . $service['title'] . '</a></li>';
                                        }
                                    } else {
                                        // Default services if none in database
                                        echo '<li><a href="services.php">Air Freight</a></li>';
                                        echo '<li><a href="services.php">Sea Freight</a></li>';
                                        echo '<li><a href="services.php">Road Freight</a></li>';
                                        echo '<li><a href="services.php">Warehousing</a></li>';
                                        echo '<li><a href="services.php">Supply Chain</a></li>';
                                    }
                                    ?>
                                </ul>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-12 col-sm-12 footer-col-4">
                            <div class="widget widget_newsletter">
                                <h3 class="widget-title">Newsletter</h3>
                                <div class="newsletter-bx">
                                    <form role="search" method="post" action="subscribe.php">
                                        <div class="input-group">
                                            <input name="email" class="form-control" placeholder="Write your email" type="email" required>
                                            <span class="input-group-btn">
                                                <button class="btn" type="submit"><i class="fa fa-paper-plane"></i></button>
                                            </span>
                                        </div>
                                    </form>
                                </div>
                                <!--Social Icons-->
                                <div class="social-icons">
                                    <ul>
                                        <?php if (isset($siteSettings['facebook_url']) && !empty($siteSettings['facebook_url'])): ?>
                                        <li><a href="<?php echo $siteSettings['facebook_url']; ?>" class="fa fa-facebook"></a></li>
                                        <?php endif; ?>
                                        <?php if (isset($siteSettings['twitter_url']) && !empty($siteSettings['twitter_url'])): ?>
                                        <li><a href="<?php echo $siteSettings['twitter_url']; ?>" class="fa fa-twitter"></a></li>
                                        <?php endif; ?>
                                        <?php if (isset($siteSettings['linkedin_url']) && !empty($siteSettings['linkedin_url'])): ?>
                                        <li><a href="<?php echo $siteSettings['linkedin_url']; ?>" class="fa fa-linkedin"></a></li>
                                        <?php endif; ?>
                                        <?php if (isset($siteSettings['instagram_url']) && !empty($siteSettings['instagram_url'])): ?>
                                        <li><a href="<?php echo $siteSettings['instagram_url']; ?>" class="fa fa-instagram"></a></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <!-- FOOTER COPYRIGHT -->
            <div class="footer-bottom">
                <div class="container">
                    <div class="wt-footer-bot-left">
                        <span class="copyrights-text">Copyright © <?php echo date('Y'); ?> by <a href="javascript:void(0);"><?php echo $siteSettings['site_name']; ?></a>. All Rights Reserved.</span>
                    </div>
                    <div class="wt-footer-bot-right">
                        <ul class="footer-bottom-nav">
                            <li><a href="privacy.php">Privacy Policy</a></li>
                            <li><a href="terms.php">Terms of Service</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </footer>
        <!-- FOOTER END -->

    </div>

    <!-- BUTTON TOP START -->
    <button class="scroltop"><span class="fa fa-angle-up relative" id="btn-vibrate"></span></button>

    <!-- JAVASCRIPT  FILES ========================================= -->
    <script src="js/jquery-3.6.0.min.js"></script><!-- JQUERY.MIN JS -->
    <script src="js/popper.min.js"></script><!-- POPPER.MIN JS -->
    <script src="js/bootstrap.min.js"></script><!-- BOOTSTRAP.MIN JS -->
    <script src="js/magnific-popup.min.js"></script><!-- MAGNIFIC-POPUP JS -->
    <script src="js/waypoints.min.js"></script><!-- WAYPOINTS JS -->
    <script src="js/counterup.min.js"></script><!-- COUNTERUP JS -->
    <script src="js/waypoints-sticky.min.js"></script><!-- STICKY HEADER -->
    <script src="js/isotope.pkgd.min.js"></script><!-- MASONRY  -->
    <script src="js/imagesloaded.pkgd.min.js"></script><!-- MASONRY  -->
    <script src="js/owl.carousel.min.js"></script><!-- OWL  SLIDER  -->
    <script src="js/theia-sticky-sidebar.js"></script><!-- STICKY SIDEBAR  -->
    <script src="js/lc_lightbox.lite.js"></script><!-- Lc light box popup -->
    <script src="js/bootstrap-slider.min.js"></script><!-- Price range slider -->
    <script src="js/jquery.bootstrap-touchspin.js"></script><!-- BOOTSTRAP TOUCHSPIN  -->
    <script src="js/jquery.bgscroll.js"></script><!-- BACKGROUND SCROLL -->
    <script src="js/slick.min.js"></script><!-- SLICK SLIDER -->
    <script src="js/swiper-bundle.min.js"></script><!-- Swiper JS -->
    <script src="js/custom.js"></script><!-- CUSTOM FUCTIONS  -->

    <!-- Initialize Lenis Smooth Scrolling -->
    <script>
        // Initialize Lenis
        const lenis = new Lenis({
            duration: 1.2,
            easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
            direction: 'vertical',
            gestureDirection: 'vertical',
            smooth: true,
            mouseMultiplier: 1,
            smoothTouch: false,
            touchMultiplier: 2,
            infinite: false,
        });

        function raf(time) {
            lenis.raf(time);
            requestAnimationFrame(raf);
        }

        requestAnimationFrame(raf);

        // Enhanced PCCS Loader with Progress
        window.addEventListener('load', function() {
            // Simulate loading progress
            let progress = 0;
            const progressBar = document.querySelector('.loading-progress::before');

            const loadingInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(loadingInterval);

                    // Hide loader with smooth transition
                    setTimeout(() => {
                        const loader = document.getElementById('pccsLoader');
                        loader.style.opacity = '0';
                        loader.style.transform = 'scale(1.1)';

                        setTimeout(() => {
                            loader.style.display = 'none';

                            // Trigger entrance animations
                            document.body.classList.add('loaded');
                            triggerEntranceAnimations();
                        }, 800);
                    }, 500);
                }
            }, 100);
        });

        // Trigger entrance animations after loader
        function triggerEntranceAnimations() {
            const elements = document.querySelectorAll('.loading-animation');
            elements.forEach((element, index) => {
                setTimeout(() => {
                    element.classList.add('animate-in');
                }, index * 100);
            });
        }

        // Advanced Logistics Animation System
        function initLogisticsAnimations() {
            // Intersection Observer for scroll animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');

                        // Special handling for different animation types
                        if (entry.target.classList.contains('tracking-steps')) {
                            animateTrackingWorkflow(entry.target);
                        }

                        if (entry.target.classList.contains('truck-animation')) {
                            startTruckMovement(entry.target);
                        }
                    }
                });
            }, observerOptions);

            // Observe all animation elements
            document.querySelectorAll('.loading-animation, .tracking-steps, .truck-animation').forEach(el => {
                observer.observe(el);
            });
        }

        // Enhanced Tracking Workflow Animation
        function animateTrackingWorkflow(container) {
            const steps = container.querySelectorAll('.tracking-step');
            const progressLine = container.querySelector('::before');

            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('active');
                    step.style.animation = `stepReveal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards`;
                    step.style.animationDelay = `${index * 0.3}s`;

                    // Add ripple effect
                    createRippleEffect(step.querySelector('.step-icon'));
                }, index * 400);
            });
        }

        // Create ripple effect for step icons
        function createRippleEffect(element) {
            const ripple = document.createElement('div');
            ripple.className = 'ripple-effect';
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
                top: 50%;
                left: 50%;
                width: 100px;
                height: 100px;
                margin-left: -50px;
                margin-top: -50px;
            `;

            element.style.position = 'relative';
            element.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Enhanced Truck Movement
        function startTruckMovement(truck) {
            let position = -100;
            const speed = 2;

            function moveTruck() {
                position += speed;
                if (position > window.innerWidth + 100) {
                    position = -100;
                }

                truck.style.transform = `translateX(${position}px)`;

                // Add exhaust smoke effect
                if (Math.random() > 0.8) {
                    createSmokeEffect(truck);
                }

                requestAnimationFrame(moveTruck);
            }

            moveTruck();
        }

        // Create smoke effect for trucks
        function createSmokeEffect(truck) {
            const smoke = document.createElement('div');
            smoke.className = 'smoke-particle';
            smoke.style.cssText = `
                position: absolute;
                width: 10px;
                height: 10px;
                background: rgba(150, 150, 150, 0.5);
                border-radius: 50%;
                pointer-events: none;
                animation: smokeRise 2s ease-out forwards;
                left: -20px;
                top: 50%;
            `;

            truck.style.position = 'relative';
            truck.appendChild(smoke);

            setTimeout(() => {
                smoke.remove();
            }, 2000);
        }

        // Parallax scrolling for background elements
        function initParallaxScrolling() {
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;

                // Animate clouds
                document.querySelectorAll('.clowd1-move, .clowd2-move, .clowd3-move').forEach((cloud, index) => {
                    const speed = 0.2 + (index * 0.1);
                    cloud.style.transform = `translateX(${scrolled * speed}px)`;
                });

                // Animate ships and planes
                document.querySelectorAll('.slide-top, .slide-top-fast').forEach(element => {
                    const speed = element.classList.contains('slide-top-fast') ? 0.3 : 0.2;
                    element.style.transform = `translateY(${Math.sin(scrolled * 0.01) * 20}px)`;
                });
            });
        }

        // Logistics workflow cycle animation
        function startLogisticsWorkflowCycle() {
            const steps = ['pickup', 'track', 'drop', 'delivery'];
            let currentStep = 0;

            setInterval(() => {
                // Reset all steps
                document.querySelectorAll('.tracking-step').forEach(step => {
                    step.classList.remove('active', 'current');
                });

                // Activate current step with special effect
                const activeStep = document.querySelector(`[data-step="${steps[currentStep]}"]`);
                if (activeStep) {
                    activeStep.classList.add('active', 'current');
                    createPulseEffect(activeStep.querySelector('.step-icon'));
                }

                currentStep = (currentStep + 1) % steps.length;
            }, 3000);
        }

        // Create pulse effect
        function createPulseEffect(element) {
            element.style.animation = 'none';
            setTimeout(() => {
                element.style.animation = 'stepPulse 1s ease-in-out';
            }, 10);
        }

        // Counter animations for statistics
        function initCounterAnimations() {
            const counters = document.querySelectorAll('.counter');

            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px'
            };

            const counterObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const counter = entry.target;
                        const target = parseInt(counter.getAttribute('data-target'));
                        const duration = 2000;
                        const increment = target / (duration / 16);
                        let current = 0;

                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            counter.textContent = Math.floor(current);
                        }, 16);

                        counterObserver.unobserve(counter);
                    }
                });
            }, observerOptions);

            counters.forEach(counter => {
                counterObserver.observe(counter);
            });
        }

        // Initialize all animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 1000,
                easing: 'ease-out-cubic',
                once: true,
                offset: 100
            });

            initLogisticsAnimations();
            initParallaxScrolling();
            startLogisticsWorkflowCycle();

            // Initialize counter animations
            initCounterAnimations();

            // Add custom CSS animations
            const style = document.createElement('style');
            style.textContent = `
                @keyframes stepReveal {
                    from {
                        opacity: 0;
                        transform: translateY(40px) scale(0.8);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }

                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }

                @keyframes smokeRise {
                    from {
                        opacity: 0.5;
                        transform: translateY(0) scale(1);
                    }
                    to {
                        opacity: 0;
                        transform: translateY(-50px) scale(2);
                    }
                }

                .animate-in {
                    animation: fadeInUp 1.2s ease-out forwards;
                }

                .tracking-step.current .step-icon {
                    box-shadow: 0 0 30px rgba(0, 123, 255, 0.8);
                    transform: scale(1.2);
                }

                .truck-animation:hover {
                    transform: scale(1.1) !important;
                    transition: transform 0.3s ease;
                }

                .slide-top:hover, .slide-top-fast:hover {
                    animation-play-state: paused;
                }
            `;
            document.head.appendChild(style);
        });
    </script>

</body>
</html>