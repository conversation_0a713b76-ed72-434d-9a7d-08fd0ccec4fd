<?php
$pageTitle = 'Contact Us';
include 'includes/header.php';

$success = '';
$error = '';

// Handle form submission
if ($_POST) {
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);

    // Validation
    if (empty($name) || empty($email) || empty($message)) {
        $error = 'Please fill in all required fields.';
    } elseif (!validateEmail($email)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Insert into database
        $data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'subject' => $subject,
            'message' => $message,
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ];

        if ($db->insert('contact_messages', $data)) {
            $success = 'Thank you for your message. We will get back to you soon!';

            // Send email notification (optional)
            $to = ADMIN_EMAIL;
            $emailSubject = 'New Contact Form Submission - ' . SITE_NAME;
            $emailBody = "New contact form submission:\n\n";
            $emailBody .= "Name: $name\n";
            $emailBody .= "Email: $email\n";
            $emailBody .= "Phone: $phone\n";
            $emailBody .= "Subject: $subject\n";
            $emailBody .= "Message: $message\n";

            $headers = "From: $email\r\n";
            $headers .= "Reply-To: $email\r\n";

            @mail($to, $emailSubject, $emailBody, $headers);
        } else {
            $error = 'Sorry, there was an error sending your message. Please try again.';
        }
    }
}
?>

<!-- INNER PAGE BANNER -->
<div class="wt-bnr-inr overlay-wraper bg-center" style="background-image:url(images/banner/1.jpg);">
    <div class="overlay-main site-bg-secondry opacity-07"></div>
    <div class="container">
        <div class="wt-bnr-inr-entry">
            <div class="banner-title-outer">
                <div class="banner-title-name">
                    <h2 class="wt-title">Contact Us</h2>
                </div>
            </div>
            <!-- BREADCRUMB ROW -->
            <div>
                <ul class="wt-breadcrumb breadcrumb-style-2">
                    <li><a href="index.php">Home</a></li>
                    <li>Contact Us</li>
                </ul>
            </div>
            <!-- BREADCRUMB ROW END -->
        </div>
    </div>
</div>
<!-- INNER PAGE BANNER END -->

<!-- CONTACT SECTION START -->
<div class="section-full p-t120 p-b90">
    <div class="container">

        <!-- Contact Info -->
        <div class="section-content">
            <div class="row">
                <div class="col-lg-8 col-md-12">
                    <div class="contact-home1-left">
                        <div class="section-head left wt-small-separator-outer">
                            <div class="wt-small-separator site-text-primary">
                                <div>Get In Touch</div>
                            </div>
                            <h2 class="wt-title">Send Us A Message</h2>
                            <p>We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
                        </div>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <form class="contact-form cons-contact-form" method="post">
                            <div class="row">
                                <div class="col-lg-6 col-md-6">
                                    <div class="form-group mb-3">
                                        <input name="name" type="text" required class="form-control" placeholder="Name *" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6">
                                    <div class="form-group mb-3">
                                        <input name="email" type="email" class="form-control" required placeholder="Email *" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6">
                                    <div class="form-group mb-3">
                                        <input name="phone" type="text" class="form-control" placeholder="Phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6">
                                    <div class="form-group mb-3">
                                        <input name="subject" type="text" class="form-control" placeholder="Subject" value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="form-group mb-3">
                                        <textarea name="message" class="form-control" rows="4" placeholder="Message *" required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <button type="submit" class="site-button">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Send Message
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-lg-4 col-md-12">
                    <div class="contact-home1-right">
                        <div class="contact-info-section">
                            <div class="contact-info-item">
                                <div class="contact-info-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="contact-info-content">
                                    <h4>Address</h4>
                                    <p><?php echo $siteSettings['address']; ?></p>
                                </div>
                            </div>

                            <div class="contact-info-item">
                                <div class="contact-info-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="contact-info-content">
                                    <h4>Phone</h4>
                                    <p><a href="tel:<?php echo str_replace(' ', '', $siteSettings['phone']); ?>"><?php echo $siteSettings['phone']; ?></a></p>
                                </div>
                            </div>

                            <div class="contact-info-item">
                                <div class="contact-info-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="contact-info-content">
                                    <h4>Email</h4>
                                    <p><a href="mailto:<?php echo $siteSettings['email']; ?>"><?php echo $siteSettings['email']; ?></a></p>
                                </div>
                            </div>

                            <div class="contact-info-item">
                                <div class="contact-info-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="contact-info-content">
                                    <h4>Business Hours</h4>
                                    <p>Mon - Fri: 9:00 AM - 6:00 PM<br>
                                    Sat: 9:00 AM - 4:00 PM<br>
                                    Sun: Closed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- CONTACT SECTION END -->

<!-- MAP SECTION START -->
<div class="section-full">
    <div class="gmap-outline">
        <div id="gmap_canvas" class="google-map">
            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.9663095343008!2d-74.00425878459418!3d40.74844097932681!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c259bf5c1654f3%3A0xc80f9cfce5383d5d!2sGoogle!5e0!3m2!1sen!2sus!4v1635959906796!5m2!1sen!2sus"
                    width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
        </div>
    </div>
</div>
<!-- MAP SECTION END -->

<style>
.contact-info-section {
    background: #f8f9fa;
    padding: 40px 30px;
    border-radius: 10px;
    margin-top: 30px;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.contact-info-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.contact-info-icon {
    width: 60px;
    height: 60px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.contact-info-icon i {
    color: white;
    font-size: 20px;
}

.contact-info-content h4 {
    margin-bottom: 10px;
    color: #333;
    font-weight: 600;
}

.contact-info-content p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

.contact-info-content a {
    color: #007bff;
    text-decoration: none;
}

.contact-info-content a:hover {
    text-decoration: underline;
}

.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}
</style>

<?php include 'includes/footer.php'; ?>