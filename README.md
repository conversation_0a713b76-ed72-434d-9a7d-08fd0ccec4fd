# PCCS Logistics - Dynamic Website

A modern, fully dynamic PHP-based logistics website with advanced animations, admin panel, and comprehensive content management system.

## Features

### 🚀 Core Features
- **Dynamic PHP Website** with MVC-style architecture
- **Admin Panel** with complete content management
- **Database-driven** content with MySQL
- **Responsive Design** with Bootstrap 5
- **Advanced Animations** with Lenis.js and AOS
- **Logistics Workflow** animations and tracking system
- **Real-time Shipment Tracking**
- **Contact Form** with email notifications
- **Blog System** with categories and tags
- **Testimonials Management**
- **Services Management**
- **SEO Optimized** with meta tags and structured data

### 🎨 UI/UX Enhancements
- **Smooth Scrolling** with Lenis.js
- **Animate On Scroll** effects with AOS library
- **Interactive Logistics Animations**:
  - Truck movement with spinning wheels
  - Ship floating animations
  - Plane flying effects
  - Cloud movement animations
- **Enhanced Loader** with PCCS logo integration
- **Gradient Overlays** and modern design elements
- **Counter Animations** for statistics
- **Hover Effects** and micro-interactions
- **Parallax Scrolling** effects
- **Responsive Design** for all devices

### 🔧 Technical Features
- **PHP 7.4+** with PDO database connections
- **MySQL Database** with comprehensive schema
- **Session-based Authentication**
- **Password Hashing** with salt for security
- **File Upload System** with validation
- **Pagination System** for admin content
- **AJAX Form Submissions**
- **Error Handling** and logging
- **Cross-browser Compatibility**

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- mod_rewrite enabled

### Setup Instructions

1. **Clone/Download** the project files to your web server directory
2. **Import Database**:
   ```bash
   mysql -u username -p database_name < database_schema.sql
   ```
3. **Configure Database**:
   - Edit `config/database.php`
   - Update database credentials
4. **Set Permissions**:
   ```bash
   chmod 755 uploads/
   chmod 644 config/database.php
   ```
5. **Access the Website**:
   - Frontend: `http://yourdomain.com/`
   - Admin Panel: `http://yourdomain.com/admin/`
   - Default Admin: username: `admin`, password: `admin123`

## File Structure

```
pccs-logistics/
├── admin/                  # Admin panel
│   ├── includes/          # Admin includes
│   ├── dashboard.php      # Admin dashboard
│   └── login.php         # Admin login
├── config/               # Configuration files
│   └── database.php      # Database config
├── includes/             # Common includes
│   ├── header.php        # Site header
│   ├── footer.php        # Site footer
│   └── functions.php     # Utility functions
├── css/                  # Stylesheets
├── js/                   # JavaScript files
├── images/               # Image assets
├── uploads/              # Upload directory
├── index.php             # Homepage
├── services.php          # Services page
├── contact.php           # Contact page
├── test_animations.php   # Animation test page
└── database_schema.sql   # Database structure
```

## Testing

### Animation Testing
Visit `test_animations.php` to test all animations:
- Tracking steps workflow
- Counter animations
- Vehicle movements
- Button hover effects
- Scroll-triggered animations

### Admin Panel Testing
1. Login to admin panel
2. Test CRUD operations for:
   - Services management
   - Blog posts
   - Testimonials
   - Site settings
   - Contact messages
   - Shipment tracking

### Performance Testing
- Page load speeds
- Animation smoothness
- Mobile responsiveness
- Cross-browser compatibility

## Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Customization

### Adding New Animations
1. Add CSS keyframes in `includes/header.php`
2. Initialize in `includes/footer.php`
3. Apply classes to HTML elements

### Modifying Database Schema
1. Update `database_schema.sql`
2. Modify PHP models in respective files
3. Update admin panel forms

### Styling Changes
- Main styles: `css/style.css`
- Custom animations: `includes/header.php`
- Admin styles: `admin/includes/header.php`

## Security Features
- Password hashing with salt
- SQL injection prevention with PDO
- XSS protection with input sanitization
- CSRF protection for forms
- File upload validation
- Session security

## Performance Optimizations
- Lazy loading for images
- Minified CSS/JS (production)
- Database query optimization
- Caching headers
- Compressed assets

## Support
For technical support or customization requests, please contact the development team.

## License
This project is proprietary software developed for PCCS Logistics.
