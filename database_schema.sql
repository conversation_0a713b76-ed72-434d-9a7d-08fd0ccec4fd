-- PCCS Logistics Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS pccs_logistics;
USE pccs_logistics;

-- Admin users table
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    salt VARCHAR(64) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Site settings table
CREATE TABLE site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    site_name VA<PERSON>HA<PERSON>(100) NOT NULL DEFAULT 'PCCS Logistics',
    site_logo VARCHA<PERSON>(255) DEFAULT 'PCCS-LOGISTICS-logo.png',
    phone VARCHAR(20) DEFAULT '2 ************',
    email VARCHAR(100) DEFAULT '<EMAIL>',
    address TEXT,
    footer_description TEXT,
    facebook_url VARCHAR(255),
    twitter_url VARCHAR(255),
    linkedin_url VARCHAR(255),
    instagram_url VARCHAR(255),
    meta_keywords TEXT,
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    short_description TEXT,
    description LONGTEXT,
    image VARCHAR(255),
    icon VARCHAR(100),
    features JSON,
    price_range VARCHAR(50),
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    meta_title VARCHAR(200),
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    excerpt TEXT,
    content LONGTEXT,
    featured_image VARCHAR(255),
    author_id INT,
    category_id INT,
    tags JSON,
    status ENUM('published', 'draft', 'archived') DEFAULT 'draft',
    views INT DEFAULT 0,
    meta_title VARCHAR(200),
    meta_description TEXT,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Blog categories table
CREATE TABLE blog_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Testimonials table
CREATE TABLE testimonials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_name VARCHAR(100) NOT NULL,
    client_position VARCHAR(100),
    client_company VARCHAR(100),
    client_image VARCHAR(255),
    testimonial TEXT NOT NULL,
    rating INT DEFAULT 5 CHECK (rating >= 1 AND rating <= 5),
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(200),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Newsletter subscribers table
CREATE TABLE newsletter_subscribers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(100) UNIQUE NOT NULL,
    status ENUM('active', 'unsubscribed') DEFAULT 'active',
    ip_address VARCHAR(45),
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL
);

-- Shipment tracking table
CREATE TABLE shipments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tracking_number VARCHAR(50) UNIQUE NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    sender_email VARCHAR(100),
    sender_phone VARCHAR(20),
    sender_address TEXT,
    receiver_name VARCHAR(100) NOT NULL,
    receiver_email VARCHAR(100),
    receiver_phone VARCHAR(20),
    receiver_address TEXT,
    service_type VARCHAR(50),
    package_description TEXT,
    weight DECIMAL(10,2),
    dimensions VARCHAR(50),
    current_status ENUM('pickup', 'in_transit', 'out_for_delivery', 'delivered', 'exception') DEFAULT 'pickup',
    estimated_delivery DATE,
    actual_delivery TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Shipment tracking history table
CREATE TABLE shipment_tracking (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shipment_id INT NOT NULL,
    status ENUM('pickup', 'in_transit', 'out_for_delivery', 'delivered', 'exception') NOT NULL,
    location VARCHAR(200),
    description TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE
);

-- Pages table for dynamic content
CREATE TABLE pages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    content LONGTEXT,
    template VARCHAR(50) DEFAULT 'default',
    status ENUM('published', 'draft') DEFAULT 'draft',
    meta_title VARCHAR(200),
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Gallery table
CREATE TABLE gallery (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200),
    image VARCHAR(255) NOT NULL,
    alt_text VARCHAR(200),
    category VARCHAR(50),
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- FAQ table
CREATE TABLE faqs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question VARCHAR(500) NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample data

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, email, password, salt, full_name, role, status) VALUES
('admin', '<EMAIL>', 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3', 'randomsalt123', 'Administrator', 'admin', 'active');

-- Insert site settings
INSERT INTO site_settings (site_name, site_logo, phone, email, address, footer_description, meta_keywords, meta_description) VALUES
('PCCS Logistics', 'PCCS-LOGISTICS-logo.png', '2 ************', '<EMAIL>', '123 Logistics Street, Transport City, TC 12345', 'Professional logistics and transportation services for all your shipping needs. We provide reliable, efficient, and cost-effective solutions.', 'logistics, shipping, transportation, cargo, freight, supply chain', 'PCCS Logistics - Professional logistics and transportation services for all your shipping needs.');

-- Insert sample services
INSERT INTO services (title, slug, short_description, description, icon, status, sort_order) VALUES
('Air Freight', 'air-freight', 'Fast and reliable air cargo services worldwide', 'Our air freight services provide the fastest delivery times for your urgent shipments. We work with major airlines to ensure your cargo reaches its destination safely and on time.', 'fa-plane', 'active', 1),
('Sea Freight', 'sea-freight', 'Cost-effective ocean shipping solutions', 'Our sea freight services offer economical shipping solutions for large volumes. We handle both FCL and LCL shipments with comprehensive tracking and documentation.', 'fa-ship', 'active', 2),
('Road Freight', 'road-freight', 'Reliable ground transportation services', 'Our road freight network covers extensive routes with modern fleet and experienced drivers. Perfect for domestic and cross-border deliveries.', 'fa-truck', 'active', 3),
('Warehousing', 'warehousing', 'Secure storage and distribution facilities', 'State-of-the-art warehousing facilities with advanced inventory management systems. We provide storage, picking, packing, and distribution services.', 'fa-warehouse', 'active', 4),
('Supply Chain Management', 'supply-chain', 'End-to-end supply chain solutions', 'Comprehensive supply chain management services to optimize your logistics operations. From procurement to delivery, we handle it all.', 'fa-cogs', 'active', 5);

-- Insert blog categories
INSERT INTO blog_categories (name, slug, description, status) VALUES
('Industry News', 'industry-news', 'Latest news and updates from the logistics industry', 'active'),
('Tips & Guides', 'tips-guides', 'Helpful tips and guides for shipping and logistics', 'active'),
('Company Updates', 'company-updates', 'Updates and announcements from PCCS Logistics', 'active');

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, excerpt, content, author_id, category_id, status, published_at) VALUES
('The Future of Logistics Technology', 'future-logistics-technology', 'Exploring how technology is revolutionizing the logistics industry', 'Technology continues to transform the logistics industry in unprecedented ways. From AI-powered route optimization to IoT tracking devices, companies are leveraging cutting-edge solutions to improve efficiency and customer satisfaction...', 1, 1, 'published', NOW()),
('5 Tips for Efficient Shipping', 'efficient-shipping-tips', 'Learn how to optimize your shipping processes for better results', 'Efficient shipping is crucial for business success. Here are five proven strategies to streamline your shipping operations and reduce costs while improving delivery times...', 1, 2, 'published', NOW());

-- Insert sample testimonials
INSERT INTO testimonials (client_name, client_position, client_company, testimonial, rating, status, sort_order) VALUES
('John Smith', 'Logistics Manager', 'ABC Manufacturing', 'PCCS Logistics has been our trusted partner for over 5 years. Their reliability and professionalism are unmatched in the industry.', 5, 'active', 1),
('Sarah Johnson', 'Supply Chain Director', 'Global Retail Corp', 'The team at PCCS consistently delivers exceptional service. Their tracking system and customer support are outstanding.', 5, 'active', 2),
('Michael Brown', 'Operations Manager', 'Tech Solutions Inc', 'We have saved significant costs and improved our delivery times since partnering with PCCS Logistics. Highly recommended!', 5, 'active', 3);

-- Insert sample FAQs
INSERT INTO faqs (question, answer, category, sort_order, status) VALUES
('How can I track my shipment?', 'You can track your shipment using our online tracking system. Simply enter your tracking number on our tracking page to get real-time updates.', 'Tracking', 1, 'active'),
('What are your shipping rates?', 'Our shipping rates depend on various factors including destination, weight, dimensions, and service type. Contact us for a personalized quote.', 'Pricing', 2, 'active'),
('Do you provide insurance for shipments?', 'Yes, we offer comprehensive insurance coverage for all shipments. Insurance rates vary based on the declared value of your goods.', 'Insurance', 3, 'active'),
('What is your delivery timeframe?', 'Delivery times vary by service type and destination. Air freight typically takes 1-3 days, sea freight 15-45 days, and road freight 2-10 days.', 'Delivery', 4, 'active');

-- Insert sample pages
INSERT INTO pages (title, slug, content, status, meta_title, meta_description) VALUES
('About Us', 'about', '<h2>About PCCS Logistics</h2><p>PCCS Logistics is a leading provider of comprehensive logistics and transportation services...</p>', 'published', 'About PCCS Logistics', 'Learn about PCCS Logistics, our history, mission, and commitment to excellence in logistics services.'),
('Privacy Policy', 'privacy', '<h2>Privacy Policy</h2><p>This privacy policy explains how we collect, use, and protect your personal information...</p>', 'published', 'Privacy Policy - PCCS Logistics', 'Read our privacy policy to understand how we handle your personal information.'),
('Terms of Service', 'terms', '<h2>Terms of Service</h2><p>These terms of service govern your use of our website and services...</p>', 'published', 'Terms of Service - PCCS Logistics', 'Review our terms of service for using PCCS Logistics services and website.');