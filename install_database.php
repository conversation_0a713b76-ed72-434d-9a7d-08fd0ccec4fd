<?php
/**
 * PCCS Logistics Database Installation Script
 *
 * This script creates the database and tables for the PCCS Logistics website
 * Run this script once to set up the database
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'pccs_logistics';

try {
    // Connect to MySQL server (without selecting database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>PCCS Logistics Database Installation</h2>";
    echo "<p>Starting database installation...</p>";

    // Read and execute SQL file
    $sql = file_get_contents('database_schema.sql');

    if ($sql === false) {
        throw new Exception("Could not read database_schema.sql file");
    }

    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    $successCount = 0;
    $errorCount = 0;

    foreach ($statements as $statement) {
        if (empty($statement)) continue;

        try {
            $pdo->exec($statement);
            $successCount++;

            // Extract table name for display
            if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                echo "<p>✓ Created table: {$matches[1]}</p>";
            } elseif (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                echo "<p>✓ Inserted data into: {$matches[1]}</p>";
            } elseif (preg_match('/CREATE DATABASE\s+(\w+)/i', $statement, $matches)) {
                echo "<p>✓ Created database: {$matches[1]}</p>";
            }

        } catch (PDOException $e) {
            $errorCount++;
            echo "<p>✗ Error executing statement: " . $e->getMessage() . "</p>";
        }
    }

    echo "<hr>";
    echo "<h3>Installation Summary</h3>";
    echo "<p>Successful operations: $successCount</p>";
    echo "<p>Errors: $errorCount</p>";

    if ($errorCount == 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ Database installation completed successfully!</p>";
        echo "<p><strong>Default Admin Login:</strong></p>";
        echo "<ul>";
        echo "<li>Username: admin</li>";
        echo "<li>Password: admin123</li>";
        echo "<li>Admin Panel: <a href='admin/login.php'>admin/login.php</a></li>";
        echo "</ul>";
        echo "<p style='color: red;'><strong>Important:</strong> Please change the default admin password after first login!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ Installation completed with errors. Please check the error messages above.</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>PCCS Logistics - Database Installation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2 { color: #333; }
        p { margin: 5px 0; }
        .success { color: green; }
        .error { color: red; }
        ul { margin: 10px 0; }
        li { margin: 5px 0; }
        hr { margin: 20px 0; }
    </style>
</head>
<body>
    <div style="max-width: 800px;">
        <h1>PCCS Logistics Database Installation</h1>
        <p>This script sets up the database and initial data for the PCCS Logistics website.</p>
        <hr>

        <?php if (!isset($pdo)): ?>
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Make sure MySQL server is running</li>
            <li>Update database credentials in this file if needed</li>
            <li>Refresh this page to run the installation</li>
        </ol>
        <?php endif; ?>

        <p><em>Note: This script should only be run once during initial setup.</em></p>
    </div>
</body>
</html>