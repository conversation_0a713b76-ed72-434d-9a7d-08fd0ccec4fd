/* Author: thewebmax team
=========================
This file contains the styling for the actual template, this
is the file you need to edit to change the look of the
template.
This files [Table of contents] are outlined below
---------------------------------------------------
** - ========================
	 GENERAL 
	 ========================
** - Title separators
** - Center title
** - Company approch
** - Counter
** - Client carousel 1
** - Client carousel 2
** - Footer News Letter
** - Footer dark version css
** - Recent Post Entry
** - Footer Dark
** - Widget Services
** - scroll top btn css
** - Pricing Table
** - inner page banner
** - Breadcrumb
** - Contact Form
** - Pagination Style
** - Blog Side Bar
** - comment form
** - Comment list
** - Single Blog Post
** - Single Post Navigation
** - Loading
** - Loading Animation Start
** - Description list
** - Section Overlay
** - What We do
** - Blog post 4
** - All Services
** - services-box-one
** - Hilite Text
** - site-button-verticle
** - touchspin input type number
** - Google map
** - Text with bg image
** - Half effect button
** - Why Choose Section
** - Estimation Section
** - Testimonials 1
** - Booking Section
** - bhoechie tab
** - tw-we-achived
** - Estimation Section
** - Services 3
** - Home 1 banner
** - Shine Animation
---------------------------------------------------
*/
@import url("https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Sacramento&amp;display=swap");
/*===GENERAL===*/
.site-text-primary {
  color: #ff8a00;
}

.site-text-black {
  color: #000;
}

.site-text-gray {
  color: #f2faff;
}

.site-text-gray-light {
  color: #f2faff;
}

.site-text-white {
  color: #fff;
}

.site-bg-primary {
  background-color: #ff8a00;
}

.site-bg-black {
  background-color: #000;
}

.site-bg-dark {
  background-color: #0b2f44;
}

.site-bg-gray {
  background-color: #f2faff;
}

.site-bg-gray-light {
  background-color: #f2faff;
}

.site-bg-light {
  background-color: #f9f9f9;
}

.site-bg-white {
  background-color: #fff;
}

.site-bg-sky {
  background-color: #1e8fd0;
}

.site-bg-sky-blue-light {
  background-color: #d6e9fa;
}

body {
  color: #3c3c3c;
  font-family: "Rubik", sans-serif;
  line-height: 1.7;
  font-weight: 400;
  padding: 0px;
  margin: 0px;
  overflow-x: hidden;
  font-size: 15px;
}

a {
  color: #0b2f44;
  outline: 0px none;
  text-decoration: none;
}

a:hover, a:focus {
  outline: 0px none;
  text-decoration: none;
}

a:active, a:hover, a:focus {
  color: inherit;
}

.overflow-hide {
  overflow: hidden;
}

img {
  border-style: none;
  height: auto;
  max-width: 100%;
  vertical-align: middle;
}

/*Float clearfix*/
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

.shadow-bx {
  box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
}

.wt-icon-box-wraper:after {
  content: "";
  display: table;
  clear: both;
}

ol.commentlist li:after {
  content: "";
  display: table;
  clear: both;
}

section, article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, summary {
  display: block;
}

ul {
  padding: 0px;
}

::selection {
  background: #484848;
  color: #fff;
}

/*===Heading & Title====*/
h1, h2, h3, h4, h5, h6 {
  font-family: "Oswald", sans-serif;
  line-height: 1.2;
  color: #0b2f44;
}

h1 {
  font-size: 60px;
  font-weight: 700;
}

h2 {
  font-size: 45px;
  font-weight: 600;
}

h3 {
  font-size: 22px;
  font-weight: 600;
}

h4 {
  font-size: 18px;
  font-weight: 600;
}

h5 {
  font-size: 16px;
  font-weight: 600;
}

h6 {
  font-size: 14px;
  font-weight: 400;
}

/*---Text align--- */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

/*===Listing Style===*/
dl, ul, ol {
  list-style-position: outside;
  padding: 0px;
}

ul, ol {
  margin-bottom: 24px;
}

ul li {
  padding: 0px;
}

ol li {
  padding: 0px;
}

.list-simple li {
  margin-bottom: 10px;
}

.list-simple li ul {
  margin-left: 15px;
  margin-top: 10px;
}

.list-simple.list-unstyled li ul {
  margin-left: 30px;
  margin-top: 10px;
}

.list-circle,
.list-angle-right,
.list-arrow,
.list-check,
.list-checked,
.list-check-circle,
.list-chevron-circle,
.list-arrow-circle,
.list-times-circle {
  margin: 0px 0px 20px 0px;
  padding: 0px;
  list-style: none;
  font-weight: 600;
}

.list-circle li,
.list-angle-right li,
.list-arrow li,
.list-check li,
.list-checked li,
.list-check-circle li,
.list-chevron-circle li,
.list-arrow-circle li,
.list-times-circle li {
  padding: 5px 5px 5px 20px;
  position: relative;
}

.list-circle li:before,
.list-angle-right li:before,
.list-arrow li:before,
.list-check li:before,
.list-checked li:before,
.list-check-circle li:before,
.list-chevron-circle li:before,
.list-arrow-circle li:before,
.list-times-circle li:before {
  font-family: "FontAwesome";
  position: absolute;
  left: 0px;
  top: 3px;
  display: block;
  font-size: 15px;
  color: #000;
}

.list-circle li:before {
  top: 6px;
  font-size: 10px;
  content: "\f111";
}

.list-angle-right li:before {
  content: "\f105";
}

.list-arrow li:before {
  content: "\f0da";
}

.list-check li:before {
  content: "\f00c";
}

.list-checked li:before {
  content: "\f046";
}

.list-check-circle li:before {
  content: "\f058";
}

.list-chevron-circle li:before {
  content: "\f138";
}

.list-arrow-circle li:before {
  content: "\f0a9";
}

.list-times-circle li:before {
  content: "\f057";
}

/*---Padding (around)---*/
.p-a0 {
  padding: 0px;
}

.p-a5 {
  padding: 5px;
}

.p-a10 {
  padding: 10px;
}

.p-a15 {
  padding: 15px;
}

.p-a20 {
  padding: 20px;
}

.p-a25 {
  padding: 25px;
}

.p-a30 {
  padding: 30px;
}

.p-a40 {
  padding: 40px;
}

.p-a50 {
  padding: 50px;
}

.p-a60 {
  padding: 60px;
}

.p-a70 {
  padding: 70px;
}

.p-a80 {
  padding: 80px;
}

.p-a90 {
  padding: 90px;
}

.p-a100 {
  padding: 100px;
}

.p-a110 {
  padding: 110px;
}

.p-a120 {
  padding: 120px;
}

.p-a130 {
  padding: 130px;
}

.p-a140 {
  padding: 140px;
}

.p-a150 {
  padding: 150px;
}

/*---Padding (top)---*/
.p-t0 {
  padding-top: 0px;
}

.p-t5 {
  padding-top: 5px;
}

.p-t10 {
  padding-top: 10px;
}

.p-t15 {
  padding-top: 15px;
}

.p-t20 {
  padding-top: 20px;
}

.p-t30 {
  padding-top: 30px;
}

.p-t40 {
  padding-top: 40px;
}

.p-t50 {
  padding-top: 50px;
}

.p-t60 {
  padding-top: 60px;
}

.p-t70 {
  padding-top: 70px;
}

.p-t80 {
  padding-top: 80px;
}

.p-t90 {
  padding-top: 90px;
}

.p-t100 {
  padding-top: 100px;
}

.p-t110 {
  padding-top: 110px;
}

.p-t120 {
  padding-top: 120px;
}

.p-t130 {
  padding-top: 130px;
}

.p-t140 {
  padding-top: 140px;
}

.p-t150 {
  padding-top: 150px;
}

/*---Padding (bottom)---*/
.p-b0 {
  padding-bottom: 0px;
}

.p-b5 {
  padding-bottom: 5px;
}

.p-b10 {
  padding-bottom: 10px;
}

.p-b15 {
  padding-bottom: 15px;
}

.p-b20 {
  padding-bottom: 20px;
}

.p-b30 {
  padding-bottom: 30px;
}

.p-b40 {
  padding-bottom: 40px;
}

.p-b50 {
  padding-bottom: 50px;
}

.p-b60 {
  padding-bottom: 60px;
}

.p-b70 {
  padding-bottom: 70px;
}

.p-b80 {
  padding-bottom: 80px;
}

.p-b90 {
  padding-bottom: 90px;
}

.p-b100 {
  padding-bottom: 100px;
}

.p-b110 {
  padding-bottom: 110px;
}

.p-b120 {
  padding-bottom: 120px;
}

.p-b130 {
  padding-bottom: 130px;
}

.p-b140 {
  padding-bottom: 140px;
}

.p-b150 {
  padding-bottom: 150px;
}

/*---Padding (left)---*/
.p-l0 {
  padding-left: 0px;
}

.p-l5 {
  padding-left: 5px;
}

.p-l10 {
  padding-left: 10px;
}

.p-l15 {
  padding-left: 15px;
}

.p-l20 {
  padding-left: 20px;
}

.p-l30 {
  padding-left: 30px;
}

.p-l40 {
  padding-left: 40px;
}

.p-l50 {
  padding-left: 50px;
}

.p-l60 {
  padding-left: 60px;
}

.p-l70 {
  padding-left: 70px;
}

.p-l80 {
  padding-left: 80px;
}

.p-l90 {
  padding-left: 90px;
}

.p-l100 {
  padding-left: 100px;
}

.p-l110 {
  padding-left: 110px;
}

.p-l120 {
  padding-left: 120px;
}

.p-l130 {
  padding-left: 130px;
}

.p-l140 {
  padding-left: 140px;
}

.p-l150 {
  padding-left: 150px;
}

/*---Padding (right)---*/
.p-r0 {
  padding-right: 0px;
}

.p-r5 {
  padding-right: 5px;
}

.p-r10 {
  padding-right: 10px;
}

.p-r15 {
  padding-right: 15px;
}

.p-r20 {
  padding-right: 20px;
}

.p-r30 {
  padding-right: 30px;
}

.p-r40 {
  padding-right: 40px;
}

.p-r50 {
  padding-right: 50px;
}

.p-r60 {
  padding-right: 60px;
}

.p-r70 {
  padding-right: 70px;
}

.p-r80 {
  padding-right: 80px;
}

.p-r90 {
  padding-right: 90px;
}

.p-r100 {
  padding-right: 100px;
}

.p-r110 {
  padding-right: 110px;
}

.p-r120 {
  padding-right: 120px;
}

.p-r130 {
  padding-right: 130px;
}

.p-r140 {
  padding-right: 140px;
}

.p-r150 {
  padding-right: 150px;
}

/*---Padding (left right)---*/
.p-lr0 {
  padding-left: 0px;
  padding-right: 0px;
}

.p-lr5 {
  padding-left: 5px;
  padding-right: 5px;
}

.p-lr10 {
  padding-left: 10px;
  padding-right: 10px;
}

.p-lr15 {
  padding-left: 15px;
  padding-right: 15px;
}

.p-lr20 {
  padding-left: 20px;
  padding-right: 20px;
}

.p-lr30 {
  padding-left: 30px;
  padding-right: 30px;
}

.p-lr40 {
  padding-left: 40px;
  padding-right: 40px;
}

.p-lr50 {
  padding-left: 50px;
  padding-right: 50px;
}

.p-lr60 {
  padding-left: 60px;
  padding-right: 60px;
}

.p-lr70 {
  padding-left: 70px;
  padding-right: 70px;
}

.p-lr80 {
  padding-left: 80px;
  padding-right: 80px;
}

.p-lr90 {
  padding-left: 90px;
  padding-right: 90px;
}

.p-lr100 {
  padding-left: 100px;
  padding-right: 100px;
}

.p-lr120 {
  padding-left: 120px;
  padding-right: 120px;
}

.p-lr150 {
  padding-left: 150px;
  padding-right: 150px;
}

/*---Padding (top bottom)---*/
.p-tb0 {
  padding-bottom: 0px;
  padding-top: 0px;
}

.p-tb5 {
  padding-bottom: 5px;
  padding-top: 5px;
}

.p-tb10 {
  padding-bottom: 10px;
  padding-top: 10px;
}

.p-tb15 {
  padding-bottom: 15px;
  padding-top: 15px;
}

.p-tb20 {
  padding-bottom: 20px;
  padding-top: 20px;
}

.p-tb30 {
  padding-bottom: 30px;
  padding-top: 30px;
}

.p-tb40 {
  padding-bottom: 40px;
  padding-top: 40px;
}

.p-tb50 {
  padding-bottom: 50px;
  padding-top: 50px;
}

.p-tb60 {
  padding-bottom: 60px;
  padding-top: 60px;
}

.p-tb70 {
  padding-bottom: 70px;
  padding-top: 70px;
}

.p-tb80 {
  padding-bottom: 80px;
  padding-top: 80px;
}

.p-tb90 {
  padding-bottom: 90px;
  padding-top: 90px;
}

.p-tb100 {
  padding-bottom: 100px;
  padding-top: 100px;
}

.p-tb120 {
  padding-bottom: 120px;
  padding-top: 120px;
}

.p-tb150 {
  padding-bottom: 150px;
  padding-top: 150px;
}

/*----Margin (around)----*/
.m-a-1 {
  margin: -1px;
}

.m-a0 {
  margin: 0px;
}

.m-a5 {
  margin: 5px;
}

.m-a10 {
  margin: 10px;
}

.m-a15 {
  margin: 15px;
}

.m-a20 {
  margin: 20px;
}

.m-a30 {
  margin: 30px;
}

.m-a40 {
  margin: 40px;
}

.m-a50 {
  margin: 50px;
}

.m-a60 {
  margin: 60px;
}

.m-a70 {
  margin: 70px;
}

.m-a80 {
  margin: 80px;
}

.m-a90 {
  margin: 90px;
}

.m-a100 {
  margin: 100px;
}

.m-a110 {
  margin: 110px;
}

.m-a120 {
  margin: 120px;
}

.m-a130 {
  margin: 130px;
}

.m-a140 {
  margin: 140px;
}

.m-a150 {
  margin: 150px;
}

/*---Marging (top)----*/
.m-t0 {
  margin-top: 0px;
}

.m-t5 {
  margin-top: 5px;
}

.m-t10 {
  margin-top: 10px;
}

.m-t15 {
  margin-top: 15px;
}

.m-t20 {
  margin-top: 20px;
}

.m-t30 {
  margin-top: 30px;
}

.m-t40 {
  margin-top: 40px;
}

.m-t50 {
  margin-top: 50px;
}

.m-t60 {
  margin-top: 60px;
}

.m-t70 {
  margin-top: 70px;
}

.m-t80 {
  margin-top: 80px;
}

.m-t90 {
  margin-top: 90px;
}

.m-t100 {
  margin-top: 100px;
}

.m-t110 {
  margin-top: 110px;
}

.m-t120 {
  margin-top: 120px;
}

.m-t130 {
  margin-top: 130px;
}

.m-t140 {
  margin-top: 140px;
}

.m-t150 {
  margin-top: 150px;
}

/*---Marging (bottom)---*/
.m-b0 {
  margin-bottom: 0px;
}

.m-b5 {
  margin-bottom: 5px;
}

.m-b10 {
  margin-bottom: 10px;
}

.m-b15 {
  margin-bottom: 15px;
}

.m-b20 {
  margin-bottom: 20px;
}

.m-b30 {
  margin-bottom: 30px;
}

.m-b40 {
  margin-bottom: 40px;
}

.m-b50 {
  margin-bottom: 50px;
}

.m-b60 {
  margin-bottom: 60px;
}

.m-b70 {
  margin-bottom: 70px;
}

.m-b80 {
  margin-bottom: 80px;
}

.m-b90 {
  margin-bottom: 90px;
}

.m-b100 {
  margin-bottom: 100px;
}

.m-b110 {
  margin-bottom: 110px;
}

.m-b120 {
  margin-bottom: 120px;
}

.m-b130 {
  margin-bottom: 130px;
}

.m-b140 {
  margin-bottom: 140px;
}

.m-b150 {
  margin-bottom: 150px;
}

/*---Marging (left)---*/
.m-l0 {
  margin-left: 0px;
}

.m-l5 {
  margin-left: 5px;
}

.m-l10 {
  margin-left: 10px;
}

.m-l15 {
  margin-left: 15px;
}

.m-l20 {
  margin-left: 20px;
}

.m-l30 {
  margin-left: 30px;
}

.m-l40 {
  margin-left: 40px;
}

.m-l50 {
  margin-left: 50px;
}

.m-l60 {
  margin-left: 60px;
}

.m-l70 {
  margin-left: 70px;
}

.m-l80 {
  margin-left: 80px;
}

.m-l90 {
  margin-left: 90px;
}

.m-l100 {
  margin-left: 100px;
}

.m-l110 {
  margin-left: 110px;
}

.m-l120 {
  margin-left: 120px;
}

.m-l130 {
  margin-left: 130px;
}

.m-l140 {
  margin-left: 140px;
}

.m-l150 {
  margin-left: 150px;
}

/*---Marging (right)---*/
.m-r0 {
  margin-right: 0px;
}

.m-r5 {
  margin-right: 5px;
}

.m-r10 {
  margin-right: 10px;
}

.m-r15 {
  margin-right: 15px;
}

.m-r20 {
  margin-right: 20px;
}

.m-r30 {
  margin-right: 30px;
}

.m-r40 {
  margin-right: 40px;
}

.m-r50 {
  margin-right: 50px;
}

.m-r60 {
  margin-right: 60px;
}

.m-r70 {
  margin-right: 70px;
}

.m-r80 {
  margin-right: 80px;
}

.m-r90 {
  margin-right: 90px;
}

.m-r100 {
  margin-right: 100px;
}

.m-r110 {
  margin-right: 110px;
}

.m-r120 {
  margin-right: 120px;
}

.m-r130 {
  margin-right: 130px;
}

.m-r140 {
  margin-right: 140px;
}

.m-r150 {
  margin-right: 150px;
}

/*---Marging (left right)---*/
.m-lr0 {
  margin-left: 0px;
  margin-right: 0px;
}

.m-lr5 {
  margin-left: 5px;
  margin-right: 5px;
}

.m-lr10 {
  margin-left: 10px;
  margin-right: 10px;
}

.m-lr15 {
  margin-left: 15px;
  margin-right: 15px;
}

.m-lr20 {
  margin-left: 20px;
  margin-right: 20px;
}

.m-lr30 {
  margin-left: 30px;
  margin-right: 30px;
}

.m-lr40 {
  margin-left: 40px;
  margin-right: 40px;
}

.m-lr50 {
  margin-left: 50px;
  margin-right: 50px;
}

.m-lr60 {
  margin-left: 60px;
  margin-right: 60px;
}

.m-lr70 {
  margin-left: 70px;
  margin-right: 70px;
}

.m-lr80 {
  margin-left: 80px;
  margin-right: 80px;
}

.m-lr90 {
  margin-left: 90px;
  margin-right: 90px;
}

.m-lr100 {
  margin-left: 100px;
  margin-right: 100px;
}

.m-lr120 {
  margin-left: 120px;
  margin-right: 120px;
}

.m-lr150 {
  margin-left: 150px;
  margin-right: 150px;
}

/*---Marging (top bottom)---*/
.m-tb0 {
  margin-bottom: 0px;
  margin-top: 0px;
}

.m-tb5 {
  margin-bottom: 5px;
  margin-top: 5px;
}

.m-tb10 {
  margin-bottom: 10px;
  margin-top: 10px;
}

.m-tb15 {
  margin-bottom: 15px;
  margin-top: 15px;
}

.m-tb20 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.m-tb30 {
  margin-bottom: 30px;
  margin-top: 30px;
}

.m-tb40 {
  margin-bottom: 40px;
  margin-top: 40px;
}

.m-tb50 {
  margin-bottom: 50px;
  margin-top: 50px;
}

.m-tb60 {
  margin-bottom: 60px;
  margin-top: 60px;
}

.m-tb70 {
  margin-bottom: 70px;
  margin-top: 70px;
}

.m-tb80 {
  margin-bottom: 80px;
  margin-top: 80px;
}

.m-tb90 {
  margin-bottom: 90px;
  margin-top: 90px;
}

.m-tb100 {
  margin-bottom: 100px;
  margin-top: 100px;
}

.m-tb120 {
  margin-bottom: 120px;
  margin-top: 120px;
}

.m-tb150 {
  margin-bottom: 150px;
  margin-top: 150px;
}

/*---Colum gap less---*/
.no-col-gap [class*="col-xs-"],
.no-col-gap [class*="col-sm-"],
.no-col-gap [class*="col-md-"],
.no-col-gap [class*="col-lg-"] {
  padding-left: 0px;
  padding-right: 0px;
}

/*===Background Effect===*/
.bg-center {
  background-position: center;
}

.bg-top-left {
  background-position: top left;
}

.bg-top-right {
  background-position: top right;
}

.bg-top-center {
  background-position: top center;
}

.bg-bottom-left {
  background-position: bottom left;
}

.bg-bottom-right {
  background-position: bottom right;
}

.bg-bottom-center {
  background-position: bottom center;
}

.bg-left-center {
  background-position: left;
}

.bg-right-center {
  background-position: left;
}

.bg-auto {
  background-size: auto;
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

.bg-full-width {
  background-size: 100% auto;
}

.bg-full-height {
  background-size: auto 100%;
}

.bg-repeat {
  background-repeat: repeat;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.bg-repeat-x {
  background-repeat: repeat-x;
}

.bg-repeat-y {
  background-repeat: repeat-y;
}

.bg-fixed {
  background-attachment: fixed;
}

.bg-scroll {
  background-attachment: scroll;
}

/*===Overlay ( transparient box style)===*/
.overlay-wraper {
  position: relative;
}

.overlay-main {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
}

.opacity-01 {
  opacity: 0.1;
}

.opacity-02 {
  opacity: 0.2;
}

.opacity-03 {
  opacity: 0.3;
}

.opacity-04 {
  opacity: 0.4;
}

.opacity-05 {
  opacity: 0.5;
}

.opacity-06 {
  opacity: 0.6;
}

.opacity-07 {
  opacity: 0.7;
}

.opacity-08 {
  opacity: 0.8;
}

.opacity-09 {
  opacity: 0.9;
}

.overlay-light .overlay-main {
  opacity: 0.3;
}

.overlay-dark .overlay-main {
  opacity: 0.9;
}

.overlay-wraper > .container,
.overlay-wraper > .container-fluid,
.overlay-wraper > .wt-icon-box-wraper,
.overlay-wraper > .wt-left-part,
.overlay-wraper > .wt-right-part {
  position: relative;
  z-index: 1;
}

/*===Boxes Css===*/
.rounded-bx,
.wt-box,
.wt-icon-box,
.wt-icon-box-small,
.wt-thum-bx,
.wt-post-thum {
  position: relative;
}

/*---Box content----*/
.wt-box {
  position: relative;
}

.wt-box.no-margin {
  margin-bottom: 0;
}

/*---For icons with box---*/
.wt-icon-box-xld {
  width: 180px;
  height: 180px;
  display: inline-block;
  text-align: center;
}

.wt-icon-box-xld i {
  font-size: 100px;
}

.wt-icon-box-xld img {
  height: 90px;
}

.wt-icon-box-xl {
  width: 150px;
  height: 150px;
  display: inline-block;
  text-align: center;
}

.wt-icon-box-xl i {
  font-size: 80px;
}

.wt-icon-box-xl img {
  height: 80px;
}

.wt-icon-box-lg {
  width: 120px;
  height: 120px;
  display: inline-block;
  text-align: center;
}

.wt-icon-box-lg i {
  font-size: 60px;
}

.wt-icon-box-lg img {
  height: 55px;
}

.wt-icon-box-md {
  width: 100px;
  height: 100px;
  display: inline-block;
  text-align: center;
}

.wt-icon-box-md i {
  font-size: 45px;
}

.wt-icon-box-md img {
  height: 40px;
}

.wt-icon-box-sm {
  width: 80px;
  height: 80px;
  display: inline-block;
  text-align: center;
}

.wt-icon-box-sm i {
  font-size: 30px;
}

.wt-icon-box-sm img {
  height: 30px;
}

.wt-icon-box-xs {
  width: 40px;
  height: 40px;
  display: inline-block;
  text-align: center;
}

.wt-icon-box-xs i {
  font-size: 20px;
}

.wt-icon-box-xs img {
  height: 20px;
}

.wt-icon-box-xld.radius,
.wt-icon-box-xl.radius,
.wt-icon-box-lg.radius,
.wt-icon-box-md.radius,
.wt-icon-box-sm.radius,
.wt-icon-box-xs.radius {
  border-radius: 100%;
}

.wt-icon-box-xld i,
.wt-icon-box-xl i,
.wt-icon-box-lg i,
.wt-icon-box-md i,
.wt-icon-box-sm i,
.wt-icon-box-xs i {
  vertical-align: middle;
}

.wt-icon-box-xld img,
.wt-icon-box-xl img,
.wt-icon-box-lg img,
.wt-icon-box-md img,
.wt-icon-box-sm img,
.wt-icon-box-xs img {
  vertical-align: middle;
  max-width: 100%;
  width: auto;
}

/*----For only icons---*/
.icon-xld,
.icon-xl,
.icon-lg,
.icon-md,
.icon-sm,
.icon-xs {
  display: inline-block;
  text-align: center;
}

.icon-xld i,
.icon-xl i,
.icon-lg i,
.icon-md i,
.icon-sm i,
.icon-xs i {
  vertical-align: middle;
}

.icon-xld img,
.icon-xl img,
.icon-lg img,
.icon-md img,
.icon-sm img,
.icon-xs img {
  vertical-align: middle;
  max-width: 100%;
  width: auto;
}

.icon-xld {
  width: 120px;
}

.icon-xld i {
  font-size: 100px;
  line-height: 100px;
}

.icon-xld.inline-icon {
  width: auto;
  text-align: left !important;
}

.icon-xld img {
  height: 90px;
}

.icon-xld.inline-icon {
  width: auto;
}

.icon-xl {
  width: 100px;
}

.icon-xl i {
  font-size: 80px;
  line-height: 80px;
}

.icon-xl.inline-icon {
  width: auto;
  text-align: left !important;
}

.icon-xl img {
  height: 80px;
}

.icon-xl.inline-icon {
  width: auto;
}

.icon-lg {
  width: 80px;
}

.icon-lg i {
  font-size: 60px;
  line-height: 60px;
}

.icon-lg.inline-icon {
  width: auto;
  text-align: left !important;
}

.icon-lg img {
  height: 70px;
}

.icon-lg.inline-icon {
  width: auto;
}

.icon-md {
  width: 60px;
}

.icon-md i {
  font-size: 45px;
  line-height: 45px;
}

.icon-md.inline-icon {
  width: auto;
  text-align: left !important;
}

.icon-md img {
  height: 50px;
}

.icon-md.inline-icon {
  width: auto;
}

.icon-sm {
  width: 40px;
}

.icon-sm i {
  font-size: 30px;
  line-height: 30px;
}

.icon-sm.inline-icon {
  width: auto;
  text-align: left !important;
}

.icon-sm img {
  height: 30px;
}

.icon-sm.inline-icon {
  width: auto;
}

.icon-xs {
  width: 30px;
}

.icon-xs i {
  font-size: 20px;
  line-height: 20px;
}

.icon-xs.inline-icon {
  width: auto;
  text-align: left !important;
}

.icon-xs img {
  height: 20px;
}

.icon-xs.inline-icon {
  width: auto;
}

/*---Place icon with box---*/
.wt-icon-box-wraper {
  position: relative;
}

.wt-icon-box-wraper .wt-tilte {
  margin-top: 0;
}

.wt-icon-box-wraper .after-titile-line {
  margin-bottom: 10px;
}

.wt-icon-box-wraper p:last-child {
  margin: 0px;
}

.icon-content {
  overflow: hidden;
}

.wt-icon-box-xld,
.wt-icon-box-xl,
.wt-icon-box-lg,
.wt-icon-box-md,
.wt-icon-box-sm,
.wt-icon-box-xs {
  display: table;
}

.wt-icon-box-xld .icon-cell,
.wt-icon-box-xl .icon-cell,
.wt-icon-box-lg .icon-cell,
.wt-icon-box-md .icon-cell,
.wt-icon-box-sm .icon-cell,
.wt-icon-box-xs .icon-cell {
  display: table-cell;
  vertical-align: middle;
}

/*---Icon box left align---*/
.wt-icon-box-wraper.left .wt-icon-box-xld,
.wt-icon-box-wraper.left .wt-icon-box-xl,
.wt-icon-box-wraper.left .wt-icon-box-lg,
.wt-icon-box-wraper.left .wt-icon-box-md,
.wt-icon-box-wraper.left .wt-icon-box-sm,
.wt-icon-box-wraper.left .wt-icon-box-xs {
  float: left;
  margin-right: 10px;
}

.wt-icon-box-wraper.left .icon-xld,
.wt-icon-box-wraper.left .icon-xl,
.wt-icon-box-wraper.left .icon-lg,
.wt-icon-box-wraper.left .icon-md,
.wt-icon-box-wraper.left .icon-sm,
.wt-icon-box-wraper.left .icon-xs {
  float: left;
  margin-right: 10px;
}

/*---Icon box right align---*/
.wt-icon-box-wraper.right {
  text-align: right;
}

.wt-icon-box-wraper.right .wt-icon-box-xld,
.wt-icon-box-wraper.right .wt-icon-box-xl,
.wt-icon-box-wraper.right .wt-icon-box-lg,
.wt-icon-box-wraper.right .wt-icon-box-md,
.wt-icon-box-wraper.right .wt-icon-box-sm,
.wt-icon-box-wraper.right .wt-icon-box-xs {
  float: right;
  display: table;
  margin-left: 20px;
}

.wt-icon-box-wraper.right .icon-xld,
.wt-icon-box-wraper.right .icon-xl,
.wt-icon-box-wraper.right .icon-lg,
.wt-icon-box-wraper.right .icon-md,
.wt-icon-box-wraper.right .icon-sm,
.wt-icon-box-wraper.right .icon-xs {
  float: right;
  margin-left: 20px;
}

/*---Icon box center align---*/
.wt-icon-box-wraper.center {
  text-align: center;
}

.wt-icon-box-wraper.center .wt-icon-box-xld,
.wt-icon-box-wraper.center .wt-icon-box-xl,
.wt-icon-box-wraper.center .wt-icon-box-lg,
.wt-icon-box-wraper.center .wt-icon-box-md,
.wt-icon-box-wraper.center .wt-icon-box-sm,
.wt-icon-box-wraper.center .wt-icon-box-xs {
  margin-left: auto;
  margin-right: auto;
}

.wt-icon-box-wraper.bx-style-1 {
  border-width: 1px;
  border-style: solid;
  border-color: #ddd;
}

.wt-icon-box-wraper.bx-style-2 {
  border-width: 1px;
  border-style: solid;
  border-color: #ddd;
}

.wt-icon-box-wraper.bx-style-2.center [class*="wt-icon-box-"] {
  position: absolute;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateY(-50%);
}

.wt-icon-box-wraper.bx-style-2.left [class*="wt-icon-box-"] {
  position: absolute;
}

.wt-icon-box-wraper.bx-style-2.right [class*="wt-icon-box-"] {
  position: absolute;
}

/*---Buttons--- */
.site-button-link {
  position: relative;
  color: #ff8a00;
  font-size: 18px;
  z-index: 1;
  transition: 0.5s all ease;
  text-transform: uppercase;
  overflow: hidden;
}

.site-button-link:after {
  content: '\f105';
  position: absolute;
  font-family: 'FontAwesome';
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0px;
  transition: 0.5s all ease;
  opacity: 0;
}

.site-button-link:hover {
  padding-left: 20px;
  color: #000;
}

.site-button-link:hover:after {
  font-size: 22px;
  left: 0px;
  opacity: 1;
}

.site-button-link.site-text-white {
  color: #fff;
}

.site-button-link.site-text-white:hover {
  color: #0b2f44;
}

/* Theme default button */
.site-button {
  outline: none;
  color: #fff;
  padding: 15px 30px;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: inline-table;
  background-color: #ff8a00;
  border: none;
  font-size: 15px;
  font-weight: 700;
  transition: 0.5s all ease;
}

.site-button:hover {
  color: #000;
}

.site-button i {
  padding-right: 10px;
}

@media (max-width: 480px) {
  .site-button {
    padding: 10px 15px;
  }
}

.btn-hover-animation {
  z-index: 1;
  overflow: hidden;
  display: inline-block;
}

.btn-hover-animation:before {
  content: "";
  position: absolute;
  z-index: -1;
  top: -4px;
  left: -14px;
  background: rgba(0, 0, 0, 0.1);
  height: 50px;
  width: 50px;
  border-radius: 50px;
  transform: scale(1);
  transform-origin: 50% 50%;
  transition: transform 1s ease-out;
  transition: transform 1s ease-out;
}

.btn-hover-animation:hover:before {
  transform: scale(8);
}

.site-button-secondry {
  color: #fff;
  padding: 15px 40px;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: inline-block;
  background-color: #000;
  border: none;
  font-size: 15px;
  font-weight: 700;
}

.site-button-secondry.sb-bdr-light {
  border-right: 6px solid #fff;
}

.site-button-secondry:active, .site-button-secondry:focus, .site-button-secondry:visited {
  background-color: #000;
  color: #fff;
}

.site-button-secondry:hover {
  background-color: #f2faff;
  color: #000;
}

/*====Button Effect====*/
.site-btn-effect {
  color: rgba(0, 0, 0, 0) !important;
  transition: all .3s ease !important;
}

.site-button.site-btn-effect {
  text-shadow: 0 0 0 #fff, 400px 0 0 #fff;
}

.site-button.site-btn-effect:hover {
  text-shadow: -400px 0 0 #000, 0 0 0 #000;
}

.site-button-secondry.site-btn-effect {
  text-shadow: 0 0 0 #fff, 400px 0 0 #fff;
}

.site-button-secondry:hover.site-btn-effect {
  text-shadow: -400px 0 0 #fff, 0 0 0   #fff;
}

.mfp-video.video-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  margin: -25px;
  color: #fff;
  display: block;
  z-index: 10;
  font-size: 14px;
  border: 2px solid #fff;
  border-radius: 50%;
}

.mfp-video.video-play-btn:hover {
  background-color: #0f1221;
  color: #fff;
  border: 2px solid transparent;
}

.mfp-video.video-play-btn i {
  margin-right: -5px;
}

/*Button text uppercase */
.site-button.text-uppercase {
  text-transform: uppercase;
}

/*Button size */
.button-sm {
  padding: 5px 10px;
  font-size: 12px;
}

.button-lg {
  padding: 15px 30px;
  font-size: 16px;
}

.button-xl {
  padding: 30px 50px;
  font-size: 24px;
}

/*Button rounded */
.radius-no {
  border-radius: 0px;
}

.radius-sm {
  border-radius: 10px;
}

.radius-md {
  border-radius: 20px;
}

.radius-xl {
  border-radius: 100px;
}

.radius-bx {
  border-radius: 100%;
}

/*===Owl Slider===*/
.owl-carousel .owl-nav {
  margin-top: 40px;
  text-align: center;
}

.owl-carousel .owl-nav .disabled {
  opacity: .5;
  cursor: default;
}

/*---Owl dots button---*/
.owl-carousel .owl-dots {
  text-align: center;
  margin-top: 30px;
}

.owl-carousel .owl-dots .owl-dot {
  display: inline-block;
}

.owl-carousel .owl-dots .owl-dot span {
  width: 6px;
  height: 6px;
  margin: 5px 7px;
  background: #051026;
  border: 2px solid transparent;
  display: block;
  border-radius: 50%;
  transition: opacity 200ms ease;
}

.owl-carousel .owl-dots .owl-dot:hover span {
  background: #000;
}

.owl-carousel .owl-dots .owl-dot.active span {
  background: none;
  border: 2px solid #051026;
  margin: 2px;
  height: 12px;
  width: 12px;
}

.owl-carousel .owl-dots .owl-dot.active span {
  background: #fff;
}

/*---Owl button top to center---*/
.owl-btn-top-center .owl-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  margin: 0;
}

.owl-btn-top-left .owl-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  margin: 0;
}

.owl-btn-top-right .owl-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  margin: 0;
}

.owl-btn-top-center .owl-stage-outer {
  padding-top: 60px;
}

.owl-btn-top-left .owl-stage-outer {
  padding-top: 60px;
}

.owl-btn-top-right .owl-stage-outer {
  padding-top: 60px;
}

.owl-btn-top-center .owl-nav {
  text-align: center;
}

/*---Owl button top to left---*/
.owl-btn-top-left .owl-nav {
  text-align: left;
}

/*---Owl button top to right---*/
.owl-btn-top-right .owl-nav {
  text-align: right;
}

/*---Owl button bottom to center [it is default position ]---*/
.owl-btn-bottom-center .owl-nav {
  text-align: center;
}

/*---Owl button bottom to left---*/
.owl-btn-bottom-left .owl-nav {
  text-align: left;
}

/*Owl button bottom to right*/
.owl-btn-bottom-right .owl-nav {
  text-align: right;
}

/*---Owl button vertical to center--*/
.owl-btn-vertical-center .owl-nav {
  margin: 0px;
}

.owl-btn-vertical-center .owl-nav .owl-prev {
  position: absolute;
  top: 50%;
  margin: -15px 0;
  left: 0;
}

.owl-btn-vertical-center .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  margin: -15px 0;
  right: 0;
}

/*---Owl button show on hover---*/
.owl-btn-hover .owl-nav {
  opacity: 0;
}

.owl-btn-hover:hover .owl-nav {
  opacity: 1;
}

.owl-carousel .owl-item img {
  transform-style: inherit;
}

/*---Owl slider button---*/
.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev,
.owl-carousel button.owl-dot {
  background-color: #0b2f44;
  color: #fff;
  padding: 8px 16px !important;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 2px;
  display: inline-block;
  cursor: pointer;
  border-radius: 0px;
  opacity: 1;
  margin-right: 5px;
  font-weight: 500;
}

.owl-carousel .owl-nav button.owl-next i,
.owl-carousel .owl-nav button.owl-prev i,
.owl-carousel button.owl-dot i {
  font-style: normal;
}

.owl-carousel .owl-nav button.owl-next:hover,
.owl-carousel .owl-nav button.owl-prev:hover,
.owl-carousel button.owl-dot:hover {
  background-color: #ff8a00;
  color: #fff;
  opacity: 0.9;
}

.owl-carousel .owl-nav button.owl-next:active,
.owl-carousel .owl-nav button.owl-prev:active,
.owl-carousel button.owl-dot:active {
  background-color: #ff8a00;
  color: #fff;
  opacity: 0.9;
}

.owl-carousel .owl-nav button.owl-next:focus,
.owl-carousel .owl-nav button.owl-prev:focus,
.owl-carousel button.owl-dot:focus {
  background-color: #ff8a00;
  color: #fff;
  opacity: 0.9;
}

.owl-btn-bottom-left .owl-nav {
  text-align: left;
}

/*---Owl dots nav position---*/
.owl-dots-bottom-left .owl-dots {
  bottom: 0px;
  padding: 15px 20px;
  position: absolute;
  width: 100%;
  text-align: left;
}

.owl-dots-bottom-right .owl-dots {
  bottom: 0px;
  padding: 15px 20px;
  position: absolute;
  width: 100%;
  text-align: right;
}

.owl-dots-bottom-center .owl-dots {
  bottom: 0px;
  padding: 15px 20px;
  position: absolute;
  width: 100%;
}

/*-------------------------------------
	Text meant only for screen readers
-------------------------------------*/
.sticky {
  clear: both;
}

.gallery-caption {
  clear: both;
}

.bypostauthor {
  clear: both;
}

.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
}

.screen-reader-text:focus {
  background-color: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  color: #21759b;
  display: block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
}

/*-------------------------------------
	Tabs 
-------------------------------------*/
/*Tabs style 1 [ default ] */
.wt-tabs .tab-pane {
  padding: 20px 0px 0px 0px;
  background-color: #fff;
}

.wt-tabs .nav-tabs {
  border: 0px;
}

.wt-tabs .nav-tabs > li {
  border: 1px solid #ebebeb;
}

.wt-tabs .nav-tabs > li > a {
  color: #000;
  background-color: #fff;
  font-size: 16px;
  padding: 15px 40px;
  border: 0px;
  display: block;
  font-weight: 600;
}

.wt-tabs .nav-tabs > li > a i {
  margin-right: 5px;
}

.wt-tabs .nav-tabs > li a.active {
  background-color: #f2faff;
  color: #000;
}

.wt-tabs .nav-tabs > li a.active:focus {
  background-color: #f2faff;
  color: #000;
}

.wt-tabs .nav-tabs > li a.active:hover {
  background-color: #f2faff;
  color: #000;
}

.wt-tabs .nav > li > a:focus {
  background-color: transparent;
  border: 0px;
}

.wt-tabs .nav > li > a:hover {
  background-color: transparent;
  border: 0px;
}

.wt-tabs .nav li a:hover {
  border: 1px;
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs {
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 767px) {
  .wt-tabs.tabs-default .tabs-default-nav .nav-tabs {
    justify-content: flex-start;
  }
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs > li > a {
  color: #000;
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs > li > a span {
  display: block;
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs > li > a span i {
  font-weight: normal;
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs > li > a.active {
  color: #fff;
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs > li.active > a {
  color: #000;
  border-color: #ddd #ddd #fff;
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs > li.active > a:focus {
  color: #000;
  border-color: #ddd #ddd #fff;
}

.wt-tabs.tabs-default .tabs-default-nav .nav-tabs > li.active > a:hover {
  color: #000;
  border-color: #ddd #ddd #fff;
}

.wt-tabs.tabs-default .tab-pane {
  color: #fff;
}

@media (max-width: 991px) {
  .wt-tabs.tabs-default .tab-pane {
    padding: 0px;
  }
}

.header-style-3 {
  position: relative;
  /*--is fixed---*/
  /*====Mobile Side =======*/
  /*---when header is sticky---*/
  /*---when header is sticky---*/
  /*---header id Fixed---*/
}

.header-style-3 .header-nav-call-section {
  float: right;
  color: #fff;
  display: flex;
}

.header-style-3 .header-nav-call-section .detail {
  text-align: right;
  margin-right: 20px;
  font-size: 34px;
  font-weight: 500;
  line-height: 0.75;
}

.header-style-3 .header-nav-call-section .detail span {
  display: block;
}

.header-style-3 .header-nav-call-section .detail span a {
  color: #fff;
}

.header-style-3 .header-nav-call-section .detail .title {
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 400;
  display: block;
  padding-bottom: 10px;
}

@media (max-width: 1199px) {
  .header-style-3 .header-nav-call-section .detail {
    font-size: 20px;
  }
}

.header-style-3 .header-nav-call-section .media img {
  opacity: 0.3;
}

@media (max-width: 540px) {
  .header-style-3 .header-nav-call-section {
    display: none;
  }
}

.header-style-3 .header-search a.header-search-icon {
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #fff;
  width: 54px;
  height: 54px;
  line-height: 54px;
  display: block;
  text-align: center;
  font-size: 24px;
  font-weight: normal;
  border-radius: 50%;
}

.header-style-3 .extra-nav {
  /*---Extra Nav---*/
  display: table;
  float: right;
  height: 70px;
  position: relative;
  z-index: 13;
}

.header-style-3 .extra-nav .extra-cell {
  display: table-cell;
  padding-left: 25px;
  vertical-align: middle;
}

.header-style-3 .extra-nav .extra-cell:first-child {
  padding-left: 0px;
}

@media (max-width: 540px) {
  .header-style-3 .extra-nav .extra-cell {
    padding-left: 10px;
  }
}

.header-style-3 .logo-header {
  /*--logo for header---*/
  display: table;
  float: left;
  width: 190px;
  height: 70px;
  vertical-align: middle;
  padding: 0px;
  font-size: 36px;
  color: #000;
  margin: 0px;
  position: relative;
  z-index: 11;
}

@media (max-width: 640px) {
  .header-style-3 .logo-header {
    width: 120px;
  }
}

@media (max-width: 360px) {
  .header-style-3 .logo-header {
    width: 120px;
  }
}

.header-style-3 .logo-header .logo-header-inner {
  display: table-cell;
  vertical-align: middle;
  height: 100%;
}

.header-style-3 .logo-header .logo-header-inner img {
  max-width: 100%;
  max-height: 100%;
}

.header-style-3.site-header {
  position: absolute;
  width: 100%;
  left: 0px;
  top: 0px;
  z-index: 999;
}

.header-style-3.site-header ul, .header-style-3.site-header ol {
  margin-bottom: 0px;
}

.header-style-3 .main-bar {
  /*--main-bar--*/
  position: relative;
  width: 100%;
  padding-top: 10px;
  padding-bottom: 10px;
  transition: all 0.5s ease;
  background: transparent;
}

.header-style-3 .main-bar .container,
.header-style-3 .main-bar .container-fluid {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 991px) {
  .header-style-3 .main-bar .container,
  .header-style-3 .main-bar .container-fluid {
    display: block;
  }
}

.header-style-3 .main-bar:after, .header-style-3 .main-bar:after {
  clear: both;
  content: "";
  display: table;
}

.header-style-3 .navbar-toggler {
  /*--Navbar Toggler---*/
  border: none;
  margin: 24px 0px 24px 15px;
  padding: 12px 10px;
}

.header-style-3 .navbar-toggler:focus {
  box-shadow: none !important;
}

@media (max-width: 991px) {
  .header-style-3 .navbar-toggler {
    display: block;
    margin-top: 14px;
    margin-bottom: 14px;
    float: right;
    outline: none !important;
  }
}

.header-style-3 .navbar-toggler .icon-bar {
  background: #fff;
  height: 3px;
  border-radius: 0px;
  display: block;
  width: 22px;
}

.header-style-3 .navbar-toggler .icon-bar + .icon-bar {
  margin-top: 4px;
}

@media (max-width: 991px) {
  .header-style-3 .navbar-toggler {
    margin-top: 14px;
    margin-bottom: 14px;
  }
}

@media (max-width: 420px) {
  .header-style-3 .navbar-toggler {
    margin-left: 10px;
  }
}

@media (max-width: 540px) {
  .header-style-3 .is-fixed .navbar-toggler {
    margin-bottom: 0px;
  }
}

.header-style-3 .is-fixed.mobile-sider-drawer-menu .header-nav .nav {
  height: 100vh !important;
  width: 100%;
}

.header-style-3 .is-fixed.mobile-sider-drawer-menu .header-nav .nav > li > a {
  padding: 12px 15px !important;
}

.header-style-3 .header-nav {
  position: relative;
  padding: 0px;
  z-index: 10;
  /*-- Submenu direction---*/
}

@media (max-width: 991px) {
  .header-style-3 .header-nav {
    clear: both;
    margin: 0px -15px;
    border-bottom: 1px solid #E9E9E9;
  }
  .header-style-3 .header-nav.navbar-collapse {
    align-items: start;
  }
}

.header-style-3 .header-nav .nav {
  display: flex;
  justify-content: flex-end;
  flex-direction: inherit;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav {
    float: none;
    margin: 0px;
    background: #fff;
    display: block;
    width: 100%;
    left: 0px;
  }
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav li {
    float: none;
  }
}

.header-style-3 .header-nav .nav li i {
  font-size: 9px;
  margin-left: 3px;
  margin-top: -3px;
  vertical-align: middle;
  opacity: 0.7;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav li i {
    float: right;
  }
}

.header-style-3 .header-nav .nav > li {
  position: relative;
  margin: -10px 0px;
  padding: 10px 0px;
  font-family: "Rubik", sans-serif;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li ul {
    display: none;
    position: static;
    visibility: visible;
    width: auto;
    background: #f9f9f9;
  }
}

.header-style-3 .header-nav .nav > li > a {
  color: #fff;
  font-size: 16px;
  padding: 15px 16px;
  cursor: pointer;
  margin: 0px 0px;
  font-weight: 400;
  display: block;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li > a {
    padding: 12px 15px;
    border-top: 1px solid #E9E9E9;
    color: #000;
  }
}

.header-style-3 .header-nav .nav > li > a:hover {
  background-color: transparent;
  color: #ff8a00;
}

.header-style-3 .header-nav .nav > li > a:active, .header-style-3 .header-nav .nav > li > a:focus {
  background-color: transparent;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li.active > a,
  .header-style-3 .header-nav .nav > li.current-menu-item > a {
    color: #000;
  }
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li > a:hover,
  .header-style-3 .header-nav .nav > li > a:active,
  .header-style-3 .header-nav .nav > li > a:focus {
    background-color: #f0f0f0;
    text-decoration: none;
  }
}

.header-style-3 .header-nav .nav > li.active > a,
.header-style-3 .header-nav .nav > li.current-menu-item > a {
  background-color: transparent;
  color: #000;
}

.header-style-3 .header-nav .nav > li:hover > a {
  color: #ff8a00;
}

.header-style-3 .header-nav .nav > li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  margin-top: 0px;
  transition: all 0.3s ease;
}

.header-style-3 .header-nav .nav > li .sub-menu {
  background-color: #fff;
  display: block;
  left: 0px;
  top: 100%;
  list-style: none;
  margin: 20px 0 0;
  opacity: 0;
  padding: 0px;
  position: absolute;
  visibility: hidden;
  width: 220px;
  z-index: 10;
  transition: none !important;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li .sub-menu {
    display: none;
    position: static;
    visibility: visible;
    width: auto;
    background: #f9f9f9;
    opacity: 1;
    margin: 0px !important;
  }
}

.header-style-3 .header-nav .nav > li .sub-menu li {
  border-bottom: 1px solid #f4f4f4;
  position: relative;
  text-transform: none;
}

.header-style-3 .header-nav .nav > li .sub-menu li a {
  color: #000;
  display: block;
  font-size: 13px;
  padding: 14px 20px;
  padding-left: 20px;
  font-weight: 500;
}

.header-style-3 .header-nav .nav > li .sub-menu li a:hover {
  color: #000;
  text-decoration: none;
}

.header-style-3 .header-nav .nav > li .sub-menu li:hover > a {
  color: #ff8a00;
}

.header-style-3 .header-nav .nav > li .sub-menu li:last-child {
  border-bottom: 0px;
}

.header-style-3 .header-nav .nav > li .sub-menu li .fa {
  color: #000;
  display: block;
  float: right;
  position: absolute;
  right: 10px;
  top: 2px;
}

.header-style-3 .header-nav .nav > li .sub-menu li > .sub-menu {
  left: 220px;
  margin: 0px 0px 0px 20px;
  transition: all 0.1s ease;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li .sub-menu li > .sub-menu > li {
    float: none;
    display: block;
    width: auto;
  }
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li .sub-menu li > .sub-menu > li a {
    padding-left: 30px;
  }
}

.header-style-3 .header-nav .nav > li .sub-menu li:hover > .sub-menu {
  left: 220px;
  margin: 0px;
  opacity: 1;
  top: -1px;
  visibility: visible;
}

.header-style-3 .header-nav .nav > li .sub-menu li:hover > .sub-menu:before {
  background-color: transparent;
  bottom: 0px;
  content: '';
  display: block;
  height: 100%;
  left: -6px;
  position: absolute;
  top: 0px;
  width: 6px;
}

.header-style-3 .header-nav .submenu-direction .sub-menu {
  left: auto;
  right: 0px;
}

.header-style-3 .header-nav .submenu-direction .sub-menu li > .sub-menu {
  left: auto;
  right: 220px;
  margin: 0px 20px 0px 0px;
}

.header-style-3 .header-nav .submenu-direction .sub-menu li:hover > .sub-menu {
  left: auto;
  right: 220px;
  margin: 0px 0px 0px 0px;
}

.header-style-3 .header-nav.nav-animation .nav > li > ul.sub-menu li > a {
  transition: all 0.5s ease-out;
  position: relative;
  z-index: 1;
}

.header-style-3 .header-nav.nav-animation .nav > li:hover > ul.sub-menu li:hover > a {
  padding-left: 40px;
}

@media (max-width: 991px) {
  .header-style-3 .has-child {
    position: relative;
  }
}

.header-style-3 .has-child .submenu-toogle {
  position: absolute;
  right: 4px;
  top: 4px;
  color: #000;
  background-color: transparent;
  z-index: 900;
  cursor: pointer;
  padding: 10px;
  font-size: 14px;
  display: none;
}

@media (max-width: 991px) {
  .header-style-3 .has-child .submenu-toogle {
    display: block;
  }
}

.header-style-3 .has-child li .submenu-toogle {
  opacity: 0.9;
}

.header-style-3 .has-child.nav-active > a + .submenu-toogle.glyphicon-chevron-right:before {
  content: "\e114";
}

.header-style-3 .has-child.nav-active > a + .submenu-toogle.glyphicon-plus::before {
  content: "\2212";
}

@media (max-width: 991px) {
  .header-style-3 .has-child.nav-active > a + .submenu-toogle.fa.fa-angle-right::before {
    content: "\f107";
  }
}

@media (max-width: 991px) {
  .header-style-3 .mobile-full-width-menu .header-nav .nav {
    position: inherit;
  }
}

@media (max-width: 991px) {
  .header-style-3.mobile-sider-drawer-menu .header-nav {
    position: fixed;
    width: 250px !important;
    z-index: 999;
    height: 100vh !important;
    overflow-y: inherit !important;
    left: -250px;
    top: 0px;
    transition: all 0.5s linear;
    background-color: #fff;
  }
  .header-style-3.mobile-sider-drawer-menu .header-nav .nav > li:hover > .sub-menu {
    animation: none !important;
  }
  .header-style-3.mobile-sider-drawer-menu .header-nav .nav > li .sub-menu li:hover > .sub-menu {
    animation: none !important;
  }
  .header-style-3.mobile-sider-drawer-menu.active .header-nav {
    left: 15px;
  }
  .header-style-3.mobile-sider-drawer-menu .icon-bar {
    transition: all 0.5s linear;
    position: relative;
  }
  .header-style-3.mobile-sider-drawer-menu #mobile-side-drawer {
    min-height: 41px;
    position: relative;
  }
  .header-style-3.mobile-sider-drawer-menu.active .icon-bar.icon-bar-first {
    transform: rotate(45deg) translateX(3px) translateY(2px);
  }
  .header-style-3.mobile-sider-drawer-menu.active .icon-bar.icon-bar-three {
    transform: rotate(-45deg) translateX(3px) translateY(-2px);
  }
  .header-style-3.mobile-sider-drawer-menu .icon-bar.icon-bar-two {
    position: relative;
    right: 0%;
  }
  .header-style-3.mobile-sider-drawer-menu.active .icon-bar.icon-bar-two {
    position: absolute;
    right: 100px;
    opacity: 0;
  }
  .header-style-3.mobile-sider-drawer-menu .is-fixed .header-nav .nav {
    overflow: auto;
    max-height: 100%;
  }
  .header-style-3.mobile-sider-drawer-menu .header-fixed .header-nav .nav {
    overflow: auto;
    max-height: 100%;
  }
}

.header-style-3 .sticky-no .main-bar {
  position: static !important;
}

.header-style-3 .is-fixed .main-bar {
  position: fixed;
  top: 0px;
  left: 0px;
}

.header-style-3 .is-fixed.color-fill .main-bar {
  background-color: #1e8fd0;
}

.header-style-3 .is-fixed.color-fill .header-nav .nav > li {
  margin: 0px;
  padding: 0px;
}

@media (max-width: 991px) {
  .header-style-3 .header-nav .nav > li {
    margin: 0px;
    padding: 0px;
  }
  .header-style-3 .header-nav .nav > li > a {
    margin: 0px;
    padding: 10px 12px;
  }
  .header-style-3 .is-fixed.color-fill .header-nav .nav > li {
    margin: 0px;
    padding: 0px;
  }
}

@media (max-width: 767px) {
  .header-style-3 .main-bar {
    padding: 0px;
  }
  .header-style-3 .is-fixed .main-bar {
    padding: 0px;
  }
  .header-style-3 .is-fixed.color-fill .main-bar {
    padding: 0px;
  }
}

.ap-btn {
  position: relative;
  padding: 8px 10px;
  color: #000;
  transition: all 600ms cubic-bezier(0.77, 0, 0.175, 1);
  cursor: pointer;
  user-select: none;
  display: inline-block;
  font-size: 18px;
  font-weight: 500;
  border-left: 2px solid #000;
  border-right: 2px solid #000;
}

.ap-btn:before {
  content: '';
  position: absolute;
  transition: inherit;
  z-index: -1;
}

.ap-btn:after {
  content: '';
  position: absolute;
  transition: inherit;
  z-index: -1;
}

.ap-btn:hover {
  color: white;
  transition-delay: .6s;
}

.ap-btn:hover:before {
  transition-delay: 0s;
}

.ap-btn:hover:after {
  background: #ff8a00;
  transition-delay: .4s;
}

.ap-btn.from-top:before {
  left: 0px;
  height: 0px;
  width: 100%;
  bottom: 0px;
  border: 3px solid #ff8a00;
  border-top: 0px;
  border-bottom: 0px;
}

.ap-btn.from-top:after {
  left: 0px;
  height: 0px;
  width: 100%;
  top: 0px;
  height: 0px;
}

.ap-btn.from-top:hover:before {
  height: 100%;
}

.ap-btn.from-top:hover:after {
  height: 100%;
}

.ap-btn.from-top.active:before {
  height: 100%;
}

.ap-btn.from-top.active:after {
  height: 100%;
}

@media (min-width: 1200px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl {
    max-width: 1170px;
  }
}

/*Title separators*/
.wt-small-separator {
  font-family: "Oswald", sans-serif;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 5px;
  color: #ff8a00;
  font-size: 40px;
  line-height: 40px;
  font-weight: 600;
  opacity: 1;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 1px #ff8a00;
}

.wt-small-separator.white {
  color: #fff;
}

@media (max-width: 991px) {
  .wt-small-separator {
    font-size: 27px;
    margin-bottom: 10px;
    letter-spacing: 3px;
  }
}

@media (max-width: 767px) {
  .wt-small-separator h2 {
    margin-bottom: 20px;
  }
}

.wt-separator-two-part-row {
  align-items: center;
}

@media (max-width: 991px) {
  .wt-separator-two-part-right {
    text-align: left;
    margin-bottom: 30px;
  }
}

/*Center title*/
.section-head.left.wt-small-separator-outer {
  text-align: left;
}

.section-head.left.wt-small-separator-outer h2 {
  margin-bottom: 35px;
}

@media (max-width: 991px) {
  .section-head.left.wt-small-separator-outer h2 {
    font-size: 30px;
  }
}

@media (max-width: 768px) {
  .section-head.left.wt-small-separator-outer h2 {
    font-size: 30px;
    margin-bottom: 20px;
  }
}

.section-head.left.wt-small-separator-outer p {
  margin-bottom: 40px;
}

.section-head.center.wt-small-separator-outer {
  margin-bottom: 50px;
  text-align: center;
  max-width: 710px;
  margin-left: auto;
  margin-right: auto;
}

.section-head.center.wt-small-separator-outer .section-head-text {
  margin: 20px 0px 0px 0px;
}

.section-head.center.wt-small-separator-outer h2 {
  max-width: 710px;
  margin: 0px auto;
}

@media (max-width: 991px) {
  .section-head.center.wt-small-separator-outer h2 {
    font-size: 30px;
  }
}

@media (max-width: 768px) {
  .section-head.center.wt-small-separator-outer h2 {
    font-size: 30px;
    margin-bottom: 20px;
  }
}

.section-head.center.wt-small-separator-outer .section-head-text {
  max-width: 660px;
  margin-left: auto;
  margin-right: auto;
}

.section-head .section-head-text {
  font-size: 14px;
}

/*Company approch*/
.tw-company-approch-section {
  position: relative;
  margin-top: 100px;
  margin-bottom: 90px;
}

@media (max-width: 575px) {
  .tw-company-approch-section {
    margin-top: 30px;
    margin-bottom: 50px;
  }
}

.tw-company-approch-section:before {
  position: absolute;
  content: '';
  top: -60px;
  bottom: -60px;
  left: 0px;
  border-style: solid;
  border-color: #ff8a00;
  border-width: 60px 0px 60px 60px;
  width: 300px;
  z-index: 0;
}

@media (max-width: 575px) {
  .tw-company-approch-section:before {
    top: -20px;
    bottom: -20px;
    left: 0px;
    border-width: 20px 0px 20px 20px;
    width: 200px;
  }
}

.tw-company-approch-section .tw-company-approch-inner {
  position: relative;
  padding: 50px;
  margin-left: 50px;
  background-color: #fff;
  box-shadow: 0px 0px 50px rgba(30, 143, 208, 0.5);
  z-index: 1;
  background-image: url(../images/background/waves.png);
  background-position: right bottom;
  background-repeat: no-repeat;
}

@media (max-width: 575px) {
  .tw-company-approch-section .tw-company-approch-inner {
    margin-left: 20px;
    padding: 30px;
  }
}

@media (max-width: 991px) {
  .tw-company-approch-section .tw-company-approch-inner .row [class*='col-']:last-child .counter-outer-two {
    margin-bottom: 0px;
  }
}

/*Counter*/
.counter-outer-two {
  position: relative;
}

@media (max-width: 991px) {
  .counter-outer-two {
    margin-bottom: 30px;
  }
}

.counter-outer-two .tw-counter-media {
  position: absolute;
  bottom: 0px;
  left: 0px;
}

.counter-outer-two .tw-counter-media img {
  height: 70px;
}

@media (max-width: 991px) {
  .counter-outer-two .tw-counter-media img {
    height: 46px;
  }
}

@media (max-width: 991px) {
  .counter-outer-two .tw-counter-media img {
    height: 46px;
  }
}

.counter-outer-two .tw-count-number {
  font-size: 46px;
  line-height: 46px;
  margin-bottom: 15px;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .counter-outer-two .tw-count-number {
    font-size: 30px;
    line-height: 30px;
  }
}

.counter-outer-two .counter {
  font-size: 46px;
  line-height: 50px;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .counter-outer-two .counter {
    font-size: 30px;
    line-height: 30px;
  }
}

@media (max-width: 768px) {
  .counter-outer-two .counter {
    font-size: 24px;
  }
}

.counter-outer-two .icon-content {
  overflow: hidden;
  padding-left: 100px;
}

@media (max-width: 991px) {
  .counter-outer-two .icon-content {
    padding-left: 80px;
  }
}

.counter-outer-two .icon-content-info {
  font-weight: 600;
  color: #0b2f44;
  margin-bottom: 0px;
}

@media (max-width: 767px) {
  .one-column1 .wt-media,
  .one-column2 .wt-media {
    margin-bottom: 20px;
  }
}

.one-column1 .wt-media img,
.one-column2 .wt-media img {
  width: 100%;
}

/*Client carousel 1*/
.home-client-carousel {
  padding: 30px 10px 10px 10px;
  background-color: #ff8a00;
  z-index: 1;
  position: relative;
  bottom: -70px;
  left: 0px;
}

@media (max-width: 991px) {
  .home-client-carousel {
    bottom: 0px;
  }
}

.home-client-carousel:after, .home-client-carousel:before {
  position: absolute;
  content: '';
  width: 60px;
  height: 60px;
}

.home-client-carousel:after {
  right: 10px;
  bottom: -10px;
  border-bottom: 1px solid #000;
  border-right: 1px solid #000;
}

.home-client-carousel:before {
  left: -10px;
  top: 10px;
  border-top: 1px solid #fff;
  border-left: 1px solid #fff;
}

.home-client-carousel.owl-carousel .owl-dots {
  margin-top: 10px;
}

.home-client-carousel .client-logo a img {
  width: auto;
  height: 70px;
  margin: auto;
}

/*Client carousel 2*/
.home-client-carousel2 {
  padding: 0px;
}

.home-client-carousel2 .client-logo {
  max-width: 130px;
  margin-left: auto;
  margin-right: auto;
}

.home-client-carousel2 .client-logo a {
  height: 100px;
  display: flex;
  justify-content: space-around;
}

.home-client-carousel2 .client-logo a img {
  width: auto;
  height: auto;
  filter: grayscale(100%);
  transition: 0.5s all ease;
}

.home-client-carousel2 .client-logo a:hover img {
  filter: none;
}

/*Footer News Letter*/
.ftr-nw-ltr {
  padding-top: 200px;
}

.ftr-nw-ltr .ftr-nw-ltr-inner {
  padding: 25px 0px;
}

.ftr-nw-ltr .ftr-nw-ltr-inner .container {
  position: relative;
}

.ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-img {
  position: absolute;
  left: 0px;
  bottom: -25px;
}

@media (max-width: 767px) {
  .ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-img {
    bottom: auto;
    top: 0px;
    position: relative;
    padding-bottom: 20px;
  }
}

.ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content {
  margin-left: 320px;
  display: flex;
  justify-content: space-between;
}

@media (max-width: 767px) {
  .ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content {
    margin-left: 0px;
  }
}

.ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content .ftr-nw-title {
  font-size: 40px;
  line-height: 1.2;
  font-family: "Oswald", sans-serif;
  color: #0b2f44;
  font-weight: 600;
  max-width: 320px;
  width: 100%;
  margin-right: 30px;
}

@media (max-width: 768px) {
  .ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content .ftr-nw-title {
    font-size: 30px;
  }
}

@media (max-width: 991px) {
  .ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content {
    display: block;
  }
  .ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content .ftr-nw-title {
    margin-right: 0px;
    max-width: 100%;
    margin-bottom: 20px;
  }
}

.ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content .ftr-nw-form {
  max-width: 476px;
  width: 100%;
}

.ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content .ftr-nw-form .form-control {
  height: 66px;
  margin-bottom: 10px;
  border: 0px;
}

.ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content .ftr-nw-form .ftr-nw-subcribe-btn {
  font-size: 22px;
  color: #fff;
  background-color: #0b2f44;
  border: none;
  font-family: "Oswald", sans-serif;
  font-weight: 600;
  padding: 9px 30px;
}

@media (max-width: 768px) {
  .ftr-nw-ltr .ftr-nw-ltr-inner .ftr-nw-content .ftr-nw-form .ftr-nw-subcribe-btn {
    font-size: 16px;
    padding: 7px 20px;
  }
}

@media (max-width: 991px) {
  .ftr-nw-ltr {
    padding-top: 105px;
  }
}

@media (max-width: 768px) {
  .ftr-nw-ltr {
    padding-top: 160px;
  }
}

@media (max-width: 767px) {
  .ftr-nw-ltr {
    padding-top: 0px;
  }
  .ftr-nw-ltr .ftr-nw-ltr-inner {
    padding: 40px 0px;
  }
}

/*Footer dark version css*/
.footer-dark {
  color: #fff;
  font-size: 16px;
}

.footer-dark .ftr-bg {
  background-color: #0b2f44;
  background-repeat: no-repeat;
  background-size: cover;
}

@media (max-width: 767px) {
  .footer-dark .ftr-bg {
    background-size: auto;
    background-repeat: repeat;
  }
}

.footer-dark .logo-footer {
  margin-bottom: 25px;
  max-width: 174px;
}

.footer-dark .footer-top {
  padding: 80px 0px 0px 0px;
}

@media (max-width: 991px) {
  .footer-dark .footer-top {
    padding: 30px 0px 0px 0px;
  }
}

.footer-dark .widget-title {
  font-family: "Rubik", sans-serif;
  color: #fff;
  font-weight: 400;
  font-size: 20px;
  margin-bottom: 30px;
  padding-bottom: 23px;
  position: relative;
}

.footer-dark .widget-title:before {
  content: '';
  position: absolute;
  bottom: 0px;
  left: 0px;
  background-color: #ff8a00;
  width: 60px;
  height: 3px;
}

.footer-dark .social-icons {
  margin: 40px 0px 0px 0px;
}

.footer-dark .social-icons li {
  display: inline-block;
}

.footer-dark .social-icons li a {
  height: 45px;
  width: 45px;
  background-color: #184f6e;
  line-height: 45px;
  padding: 0px;
  color: #fff;
  font-size: 22px;
  text-align: center;
  margin-right: 3px;
}

.footer-dark .social-icons li a:hover {
  color: #fff;
  background-color: #051721;
}

.footer-dark .footer-bottom {
  padding: 0px;
  color: #fff;
  position: relative;
  z-index: 1;
  font-weight: 400;
}

.footer-dark .footer-bottom .footer-bottom-info {
  display: flex;
  padding: 30px 0px;
  justify-content: center;
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 991px) {
  .footer-dark .footer-bottom .footer-bottom-info {
    display: block;
  }
}

@media (max-width: 991px) {
  .footer-dark .footer-bottom .footer-copy-right {
    margin: 5px 10px 5px 0px;
    display: inline-block;
  }
}

.footer-dark .footer-bottom .footer-copy-right .copyrights-text {
  color: #fff;
  font-size: 14px;
}

.widget {
  margin-bottom: 40px;
}

.ftr-list-center {
  display: grid;
  justify-content: center;
}

@media (max-width: 991px) {
  .ftr-list-center {
    justify-content: left;
  }
}

/*Recent Post Entry*/
.recent-posts-entry .widget-post {
  margin-bottom: 20px;
}

.recent-posts-entry .widget-post:last-child {
  border: none;
  margin-bottom: 0px;
}

.recent-posts-entry .wt-post-date {
  background-color: #0e0e0e;
  width: 50px;
  height: 60px;
  float: left;
}

.recent-posts-entry .wt-post-date strong {
  display: block;
  font-size: 24px;
}

.recent-posts-entry .wt-post-date span {
  display: block;
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 600;
}

.recent-posts-entry .wt-post-media {
  width: 90px;
  float: left;
}

@media (max-width: 1024px) {
  .recent-posts-entry .wt-post-media {
    width: 60px;
  }
}

.recent-posts-entry .wt-post-info {
  margin-left: 105px;
}

@media (max-width: 1024px) {
  .recent-posts-entry .wt-post-info {
    margin-left: 70px;
  }
}

.recent-posts-entry .wt-post-info .wt-post-header {
  margin-bottom: 6px;
}

.recent-posts-entry .wt-post-info .wt-post-meta ul {
  list-style: none;
  margin-bottom: 0px;
}

.recent-posts-entry .wt-post-info .wt-post-meta ul li {
  font-size: 12px;
  line-height: 16px;
  color: #ff8a00;
}

.recent-posts-entry .wt-post-info .post-title {
  margin-top: 0px;
}

/*Footer Dark*/
.footer-dark .recent-posts-entry .wt-post-info .wt-post-header a {
  color: #acacac;
}

/*Widget Services*/
.widget_services ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.widget_services ul li {
  display: block;
  width: 100%;
  padding: 2px 0px;
  border: 0px;
  overflow: hidden;
}

.widget_services ul li a {
  color: #fff;
  position: relative;
  padding: 0px 0px 5px 0px;
  display: block;
  margin-left: 0px;
  transition: 0.5s all ease;
  font-size: 16px;
}

.widget_services ul li a:hover {
  color: #ff8a00;
}

/*scroll top btn css*/
button.scroltop {
  height: 40px;
  width: 40px;
  background: #1d1b18;
  border: none;
  position: fixed;
  right: 15px;
  bottom: 15px;
  text-transform: uppercase;
  margin: 0;
  padding: 0;
  cursor: pointer;
  display: none;
  text-align: center;
  z-index: 999;
  color: #fff;
}

button.scroltop span {
  display: block;
  font-size: 24px;
  line-height: 24px;
}

/*Pricing Table*/
.pricing-table-1 .p-table-title {
  position: relative;
  text-align: right;
}

.pricing-table-1 .p-table-title .wt-title {
  font-size: 22px;
  margin-bottom: 0px;
  background-color: #1e8fd0;
  color: #fff;
  text-transform: uppercase;
  display: inline-block;
  padding: 10px 25px;
}

.pricing-table-1 .p-table-inner {
  position: relative;
  padding: 40px;
  border: 1px solid #1e8fd0;
}

.pricing-table-1 .p-table-inner .p-table-list ul {
  list-style: none;
  margin-bottom: 40px;
}

.pricing-table-1 .p-table-inner .p-table-list ul li {
  position: relative;
  font-size: 16px;
  margin-bottom: 5px;
  padding-left: 20px;
}

.pricing-table-1 .p-table-inner .p-table-list ul li:after {
  content: '\f105';
  font-family: 'FontAwesome';
  color: #ff8a00;
  left: 0px;
  top: 0px;
  position: absolute;
}

.pricing-table-1 .p-table-inner .p-table-price {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  color: #0b2f44;
}

.pricing-table-1 .p-table-inner .p-table-price span {
  font-family: "Oswald", sans-serif;
  font-size: 45px;
  line-height: 56px;
  font-weight: 600;
  display: block;
  padding-right: 15px;
}

@media (max-width: 991px) {
  .pricing-table-1 .p-table-inner .p-table-price span {
    font-size: 36px;
    line-height: 26px;
  }
}

.pricing-table-1 .p-table-inner .p-table-price p {
  font-size: 22px;
  margin-bottom: 0px;
  font-family: "Oswald", sans-serif;
}

.p-table-highlight .p-table-inner {
  background-color: #1e8fd0;
}

.p-table-highlight .p-table-inner .p-table-price {
  color: #fff;
}

.p-table-highlight .p-table-inner .p-table-list ul li {
  color: #fff;
}

/*inner page banner*/
.wt-bnr-inr {
  height: 510px;
  background-size: cover;
  background-position: center center;
  display: table;
  width: 100%;
  position: relative;
}

@media (max-width: 767px) {
  .wt-bnr-inr {
    height: 380px;
  }
}

.wt-bnr-inr:after {
  content: '';
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  position: absolute;
  background: linear-gradient(to bottom, #1e8fd0 0%, rgba(30, 143, 208, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1e8fd0', endColorstr='#001e8fd0',GradientType=0 );
}

.wt-bnr-inr .container {
  display: table;
  height: 100%;
}

.wt-bnr-inr-entry {
  display: table-cell;
  vertical-align: bottom;
  text-align: left;
  padding-bottom: 10px;
}

.wt-bnr-inr-entry .banner-title-outer {
  position: relative;
}

.wt-bnr-inr-entry .banner-title-outer .wt-title {
  font-size: 66px;
  margin-bottom: 0px;
  position: relative;
}

@media (max-width: 767px) {
  .wt-bnr-inr-entry .banner-title-outer .wt-title {
    font-size: 40px;
  }
}

.wt-bnr-inr-entry .banner-title-outer .banner-title-name {
  display: inline-block;
  margin-bottom: 50px;
}

.wt-bnr-inr-entry .banner-title-outer .banner-title-name .wt-title {
  color: #fff;
}

/*Breadcrumb*/
.wt-breadcrumb {
  margin: 0;
  display: inline-block;
  list-style: none;
  position: relative;
}

.wt-breadcrumb:before {
  content: '';
  right: -15px;
  background-color: #f2faff;
  position: absolute;
  bottom: -11px;
  width: 6000px;
  height: 60px;
}

.wt-breadcrumb li {
  padding-right: 30px;
  position: relative;
  display: inline-block;
  font-size: 18px;
  font-family: "Rubik", sans-serif;
  font-weight: 600;
  color: #ff8a00;
  text-transform: uppercase;
}

.wt-breadcrumb li a {
  color: #0b2f44;
}

.wt-breadcrumb li a:hover {
  color: #ff8a00;
}

.wt-breadcrumb li:after {
  content: '//';
  position: absolute;
  right: 8px;
  top: 3px;
  color: #0b2f44;
  font-size: 12px;
  font-weight: 600;
}

.wt-breadcrumb li:last-child {
  color: #ff8a00;
  padding-right: 0px;
}

.wt-breadcrumb li:last-child:after {
  display: none;
}

.wt-breadcrumb li:first-child a i {
  font-size: 18px;
  vertical-align: text-top;
}

/*Contact Form*/
.contact-one .contact-one-inner {
  margin: 0px;
}

.contact-one .contact-info {
  padding: 40px;
  position: relative;
  z-index: 1;
}

@media (max-width: 575px) {
  .contact-one .contact-info {
    padding: 30px;
  }
}

.contact-one .contact-info-section .c-info-column {
  padding: 20px 20px 20px 70px;
  color: #fff;
  position: relative;
  min-height: 75px;
}

.contact-one .contact-info-section .c-info-column p {
  font-size: 14px;
}

.contact-one .contact-info-section .c-info-column .c-info-icon {
  position: absolute;
  font-size: 32px;
  line-height: 32px;
  left: 0px;
  top: 20px;
  color: #fff;
  width: 50px;
  height: 50px;
  text-align: center;
  z-index: 1;
}

.contact-one .contact-info-section .c-info-column .c-info-icon.custome-size {
  font-size: 44px;
}

.contact-one .contact-info-section .c-info-column .c-info-icon span {
  width: 50px;
  height: 50px;
  position: absolute;
  z-index: -1;
  background-color: #0b2f44;
  top: 0px;
  left: 0px;
}

.contact-one .contact-info-section .c-info-column .c-info-icon:after {
  content: '';
  position: absolute;
  border: 1px solid #ff8a00;
  right: -5px;
  bottom: -5px;
  height: 50px;
  width: 50px;
  z-index: -2;
}

.contact-one .contact-info-section .c-info-column span {
  font-weight: 600;
  font-size: 18px;
  display: block;
  margin-bottom: 10px;
}

.contact-one .contact-info-section .c-info-column p {
  margin-bottom: 0px;
}

.contact-one .contact-info-section .c-info-column p a {
  color: #fff;
}

@media (max-width: 991px) {
  .contact-one {
    margin-bottom: 20px;
  }
}

@media (max-width: 575px) {
  .contact-one {
    padding: 0px;
    box-shadow: none;
  }
}

.section-head-small {
  font-size: 22px;
  border-left: 4px solid #ff8a00;
  padding-left: 20px;
}

.section-head-small.white .wt-title {
  color: #fff;
}

.contact-form-outer {
  padding: 40px 25px;
}

.cons-contact-form .form-control {
  height: 52px;
  padding: 10px 20px;
  background-color: #fff;
  border: 0px;
}

.cons-contact-form .form-control:focus {
  box-shadow: none;
  background-color: #f2faff;
}

.cons-contact-form textarea.form-control {
  padding-top: 15px;
  height: 80px;
  margin-bottom: 40px;
  resize: none;
}

.form-control {
  height: 50px;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 12px;
  border-radius: 0px;
}

.form-control:focus {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.6);
  background-color: #F8F8F8;
}

/*Pagination Style*/
.pagination-outer {
  padding-top: 30px;
}

@media (max-width: 991px) {
  .pagination-outer {
    margin-bottom: 30px;
  }
}

.pagination-outer .pagination-style1 {
  position: relative;
  display: inline-block;
}

.pagination-outer .pagination-style1 ul {
  list-style: none;
  margin-bottom: 0px;
}

.pagination-outer .pagination-style1 ul li {
  position: relative;
  margin: 0px 3px 0px;
  display: inline-block;
}

.pagination-outer .pagination-style1 ul li.prev a {
  width: 40px;
  height: 40px;
  font-size: 16px;
  color: #0b2f44;
  font-weight: 700;
  transition: 0.5s all ease;
  letter-spacing: 1px;
}

@media (max-width: 420px) {
  .pagination-outer .pagination-style1 ul li.prev a {
    width: 30px;
    height: 30px;
  }
}

.pagination-outer .pagination-style1 ul li.next a {
  width: 40px;
  height: 40px;
  font-size: 16px;
  color: #0b2f44;
  font-weight: 700;
  transition: 0.5s all ease;
  letter-spacing: 1px;
}

@media (max-width: 420px) {
  .pagination-outer .pagination-style1 ul li.next a {
    width: 30px;
    height: 30px;
  }
}

.pagination-outer .pagination-style1 ul li a {
  position: relative;
  width: 40px;
  height: 40px;
  color: #0b2f44;
  font-size: 16px;
  line-height: 40px;
  text-align: center;
  display: inline-block;
  transition: 0.5s all ease;
  font-family: "Oswald", sans-serif;
  border: 1px solid #ddd;
  font-weight: 800;
}

@media (max-width: 480px) {
  .pagination-outer .pagination-style1 ul li a {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
}

.pagination-outer .pagination-style1 ul li.active a {
  background-color: #ff8a00;
  color: #fff;
  border: 1px solid #ff8a00;
}

.pagination-outer .pagination-style1 ul li:hover a {
  background-color: #ff8a00;
  border: 1px solid #ff8a00;
}

/*Blog Side Bar*/
.side-bar .widget:last-child {
  margin-bottom: 0px;
}

.side-bar .widget .form-control {
  background-color: #f2faff;
}

.side-bar .widget .widget-title {
  position: relative;
  padding-bottom: 15px;
  text-align: center;
}

.side-bar .widget .widget-title:before {
  content: '';
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff8a00;
  width: 20px;
  height: 2px;
}

.side-bar .search-bx .input-group .form-control {
  height: 80px;
  background-color: #fff;
  font-size: 20px;
  padding: 0px 25px;
}

@media (max-width: 767px) {
  .side-bar .search-bx .input-group .form-control {
    height: 60px;
  }
}

.side-bar .search-bx .input-group .btn {
  background-color: #ff8a00;
  color: #fff;
  border-radius: 0px;
  height: 80px;
  padding: 0px 30px;
  font-size: 30px;
}

@media (max-width: 767px) {
  .side-bar .search-bx .input-group .btn {
    height: 60px;
    padding: 0px 15px;
  }
}

.side-bar .widget_services ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
  background-color: #fff;
}

.side-bar .widget_services ul li {
  position: relative;
  padding: 10px 0px 10px 15px;
  line-height: 20px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
}

.side-bar .widget_services ul li:after {
  content: '';
  width: 4px;
  height: 5px;
  border-radius: 50%;
  background-color: #ff8a00;
  position: absolute;
  left: 0px;
  top: 17px;
}

.side-bar .widget_services ul li:last-child {
  padding-bottom: 0px;
}

.side-bar .widget_services ul li a {
  color: #0b2f44;
  margin-left: 0px;
  padding: 0px;
  display: inline-block;
  font-family: "Oswald", sans-serif;
  font-size: 14px;
  font-weight: 600;
}

.side-bar .widget_services ul li a:hover {
  color: #ff8a00;
}

.side-bar .widget_services ul li a i {
  padding-right: 5px;
}

.side-bar .widget_services ul li a:before {
  display: none;
}

.side-bar .widget_services ul li .badge {
  background: none;
  font-size: 14px;
  font-weight: 600;
}

.side-bar .recent-posts-entry .widget-post {
  margin-bottom: 20px;
}

.side-bar .recent-posts-entry .widget-post:last-child {
  border: none;
  margin-bottom: 0px;
}

.side-bar .recent-posts-entry .wt-post-media {
  width: 108px;
  float: left;
}

@media (max-width: 420px) {
  .side-bar .recent-posts-entry .wt-post-media {
    width: 80px;
  }
}

.side-bar .recent-posts-entry .wt-post-media img {
  border-radius: 0px;
}

.side-bar .recent-posts-entry .wt-post-info {
  margin-left: 130px;
}

@media (max-width: 420px) {
  .side-bar .recent-posts-entry .wt-post-info {
    margin-left: 95px;
  }
}

.side-bar .recent-posts-entry .wt-post-info .post-date {
  margin-top: 0px;
  color: #ff8a00;
  font-size: 16px;
  display: block;
}

.side-bar .recent-posts-entry .wt-post-info .post-title {
  margin-top: 0px;
  font-family: "Oswald", sans-serif;
  font-size: 18px;
  font-weight: 500;
  display: block;
}

.tw-sidebar-tags-wrap .tagcloud {
  margin-top: -10px;
}

.tw-sidebar-tags-wrap .tagcloud a {
  padding: 6px 15px;
  margin-top: 10px;
  margin-right: 10px;
  display: inline-block;
  color: #0b2f44;
  font-family: "Rubik", sans-serif;
  font-size: 14px;
  background-color: #f2faff;
  text-transform: uppercase;
  font-weight: 500;
}

.tw-sidebar-tags-wrap .tagcloud a:hover {
  background-color: #ff8a00;
  color: #fff;
}

ol.comment-list {
  list-style: none;
  background-color: #f1f1f1;
  padding: 30px;
}

ol.comment-list li.comment {
  position: relative;
  padding: 0;
}

ol.comment-list li.comment .comment-body {
  padding: 0px 90px 20px 120px;
  position: relative;
  margin-bottom: 30px;
  overflow: hidden;
  min-height: 130px;
}

@media (max-width: 480px) {
  ol.comment-list li.comment .comment-body {
    padding: 0px 0px 50px 0px;
  }
}

ol.comment-list li.comment .comment-author {
  display: inline-block;
  margin-bottom: 5px;
  position: absolute;
  top: 0px;
  left: 0px;
}

@media (max-width: 480px) {
  ol.comment-list li.comment .comment-author {
    position: inherit;
    margin-bottom: 20px;
  }
}

ol.comment-list li.comment .comment-author .avatar {
  width: 100px;
  height: 100px;
  transition: all .5s ease-in-out;
  padding: 5px;
  border: 1px solid #0b2f44;
}

ol.comment-list li.comment .fn {
  display: inline-block;
  color: #0b2f44;
  font-size: 18px;
  font-weight: 500;
  font-style: normal;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 480px) {
  ol.comment-list li.comment .fn {
    display: block;
  }
}

ol.comment-list li.comment .comment-meta {
  margin-bottom: 5px;
  display: block;
  background-color: #0b2f44;
  text-align: center;
}

ol.comment-list li.comment .comment-meta a {
  color: #fff;
  font-size: 12px;
  text-align: center;
}

ol.comment-list li.comment .reply {
  position: absolute;
  right: 0px;
  top: 0px;
}

@media (max-width: 480px) {
  ol.comment-list li.comment .reply {
    top: auto;
    right: auto;
    bottom: 10px;
    left: 0px;
  }
}

ol.comment-list li.comment .reply .comment-reply-link {
  position: relative;
  color: #fff;
  background-color: #ff8a00;
  padding: 4px 12px;
  font-size: 14px;
  display: inline-block;
}

ol.comment-list li.comment .reply .comment-reply-link:hover {
  color: #fff;
  background-color: #000;
}

ol.comment-list li.comment p {
  margin: 0px;
  font-weight: 400;
}

ol.comment-list li .children {
  list-style: none;
  margin-left: 40px;
}

@media (max-width: 767px) {
  ol.comment-list li .children {
    margin-left: 0px;
  }
}

ol.comment-list li .children li {
  padding: 0;
}

ol.comment-list li .children li:last-child .comment-body {
  margin-bottom: 0px;
}

/*comment form*/
@media (max-width: 991px) {
  .comment-respond {
    margin-bottom: 40px;
  }
}

@media (max-width: 480px) {
  .comment-respond {
    padding: 0px;
  }
}

.comment-respond .comment-reply-title {
  font-size: 24px;
  margin-bottom: 20px;
  font-weight: 700;
}

.comment-respond .form-submit .site-button {
  position: relative;
}

/*Comment list*/
.comments-area {
  padding: 0;
}

.comments-area .comments-title {
  font-size: 24px;
  margin-bottom: 20px;
}

.comments-area .comment-form {
  padding: 30px;
  background-color: #d6e9fa;
}

@media (max-width: 480px) {
  .comments-area .comment-form {
    margin: 0 -5px;
  }
}

.comments-area .comment-form .comment-form-author label,
.comments-area .comment-form .comment-form-email label,
.comments-area .comment-form .comment-form-comment label {
  display: none;
  line-height: 18px;
  margin-bottom: 10px;
}

.comments-area .comment-form .comment-form-author input[type="text"],
.comments-area .comment-form .comment-form-email input[type="text"],
.comments-area .comment-form .comment-form-comment input[type="text"] {
  width: 100%;
  height: 60px;
  padding: 20px;
  border: 1px solid #f2faff;
}

.comments-area .comment-form .comment-form-author input[type="text"].form-control:focus,
.comments-area .comment-form .comment-form-email input[type="text"].form-control:focus,
.comments-area .comment-form .comment-form-comment input[type="text"].form-control:focus {
  box-shadow: none;
}

.comments-area .comment-form .comment-form-comment textarea {
  width: 100%;
  padding: 20px;
  height: 120px;
  resize: none;
  border: 1px solid #f2faff;
}

.comments-area .comment-form .comment-form-comment textarea.form-control:focus {
  box-shadow: none;
}

ol.comment-list li.comment .comment-respond .comment-form p {
  padding: 0px 15px !important;
}

.comment-form .form-control:-webkit-input-placeholder {
  color: #777;
  font-size: 14px;
}

/*Single Blog Post*/
.blog-post-single-outer .blog-post-single .wt-post-meta-list {
  margin-bottom: 5px;
  width: 100%;
  clear: both;
  display: inline-block;
}

.blog-post-single-outer .blog-post-single .wt-post-meta-list .wt-list-content {
  float: left;
  margin-right: 30px;
  position: relative;
  color: #6e6e6e;
  font-size: 16px;
}

.blog-post-single-outer .blog-post-single .wt-post-meta-list .wt-list-content::after {
  content: '/';
  position: absolute;
  right: -20px;
  top: 0px;
}

.blog-post-single-outer .blog-post-single .wt-post-meta-list .wt-list-content:last-child:after {
  display: none;
}

.blog-post-single-outer .blog-post-single .wt-post-info {
  padding-top: 0px;
}

.blog-post-single-outer .blog-post-single .post-title {
  font-size: 36px;
  margin-bottom: 15px;
}

@media (max-width: 991px) {
  .blog-post-single-outer .blog-post-single .post-title {
    font-size: 30px;
  }
}

.blog-post-single-outer .blog-post-single .wt-post-media img {
  width: 100%;
}

.blog-post-single-outer .blog-post-single .wt-post-media .wt-post-meta {
  position: absolute;
  left: 0px;
  bottom: 0px;
}

.blog-post-single-outer .blog-post-single .wt-post-media .wt-post-meta ul {
  background-color: #ff8a00;
  display: inline-block;
  margin-bottom: 0px;
}

.blog-post-single-outer .blog-post-single .wt-post-media .wt-post-meta ul li {
  color: #fff;
  display: inline-table;
  font-size: 14px;
  text-align: center;
  padding: 8px 10px;
  font-weight: 500;
  position: relative;
}

.blog-post-single-outer .blog-post-single .wt-post-media .wt-post-meta ul li:after {
  position: absolute;
  content: '|';
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
}

.blog-post-single-outer .blog-post-single .wt-post-media .wt-post-meta ul li a {
  color: #fff;
}

.blog-post-single-outer .blog-post-single .wt-post-media .wt-post-meta ul li:last-child:after {
  display: none;
}

.blog-post-single-outer .blog-post-single blockquote {
  background: #d6e9fa;
  margin: 30px 0px;
  padding: 30px 30px 30px 100px;
  position: relative;
  overflow: hidden;
  font-family: "Rubik", sans-serif;
}

.blog-post-single-outer .blog-post-single blockquote p {
  font-size: 20px;
  font-weight: 400;
  color: #0b2f44;
  margin-bottom: 0px;
}

@media (max-width: 767px) {
  .blog-post-single-outer .blog-post-single blockquote p {
    font-size: 18px;
  }
}

.blog-post-single-outer .blog-post-single blockquote img {
  opacity: 0.2;
  position: absolute;
  left: 30px;
  top: 24px;
  width: 160px;
}

@media (max-width: 420px) {
  .blog-post-single-outer .blog-post-single blockquote {
    padding: 30px;
  }
  .blog-post-single-outer .blog-post-single blockquote img {
    position: inherit;
    top: auto;
    left: auto;
  }
}

.blog-post-single-outer .post-social-icons-wrap {
  float: right;
}

@media (max-width: 991px) {
  .blog-post-single-outer .post-social-icons-wrap {
    float: none;
    margin-top: 30px;
  }
}

.post-area-tags-wrap {
  border-top: 1px solid #dedede;
  margin-top: 30px;
  padding-top: 30px;
}

.post-social-icons {
  margin: 0px;
}

.post-social-icons li {
  display: inline-block;
}

.post-social-icons li a {
  height: 34px;
  width: 34px;
  background-color: #d6e9fa;
  line-height: 34px;
  padding: 0px;
  color: #0b2f44;
  font-size: 18px;
  text-align: center;
  margin-right: 3px;
}

.post-social-icons li a:hover {
  color: #fff;
  background-color: #051721;
}

/*Single Post Navigation*/
.post-navigation {
  margin-bottom: 36px;
}

.post-navigation .post-nav-links {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  padding: 30px 0px;
  border-top: 1px solid #dedede;
  border-bottom: 1px solid #dedede;
  position: relative;
}

.post-navigation .post-nav-links:after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: #dedede;
  position: absolute;
  left: 50%;
  top: 0px;
}

.post-navigation .post-nav-links .post-nav-item {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 50%;
}

@media (max-width: 640px) {
  .post-navigation .post-nav-links .post-nav-item {
    display: block;
  }
}

.post-navigation .post-nav-links .post-nav-item .nav-post-arrow {
  margin-right: 20px;
}

@media (max-width: 640px) {
  .post-navigation .post-nav-links .post-nav-item .nav-post-arrow {
    margin-bottom: 10px;
  }
}

.post-navigation .post-nav-links .post-nav-item .nav-post-arrow i {
  font-size: 50px;
  line-height: 0.70;
  color: #ff8a00;
}

.post-navigation .post-nav-links .post-nav-item .nav-post-meta {
  padding-right: 50px;
}

@media (max-width: 480px) {
  .post-navigation .post-nav-links .post-nav-item .nav-post-meta {
    padding-right: 10px;
  }
}

.post-navigation .post-nav-links .post-nav-item .nav-post-meta label {
  color: #616161;
  font-size: 14px;
  text-transform: uppercase;
  display: block;
  margin-bottom: 3px;
}

.post-navigation .post-nav-links .post-nav-item .nav-post-meta a {
  font-size: 18px;
  line-height: 24px;
  color: #0b2f44;
  font-weight: 500;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 640px) {
  .post-navigation .post-nav-links .post-nav-item .nav-post-meta a {
    font-size: 16px;
    line-height: 16px;
    font-weight: 600;
  }
}

.post-navigation .post-nav-links .post-nav-item.nav-post-next {
  flex-direction: row-reverse;
  text-align: right;
}

.post-navigation .post-nav-links .post-nav-item.nav-post-next .nav-post-meta {
  padding-left: 50px;
  padding-right: 0px;
}

@media (max-width: 480px) {
  .post-navigation .post-nav-links .post-nav-item.nav-post-next .nav-post-meta {
    padding-left: 10px;
  }
}

.post-navigation .post-nav-links .post-nav-item.nav-post-next .nav-post-arrow {
  margin-right: 0;
  margin-left: 20px;
}

@media (max-width: 991px) {
  .section-full.p-t120, .section-full.p-b90, .section-full.p-tb120 {
    padding-top: 38px;
    padding-bottom: 10px;
  }
}

/*Loading*/
.loading-area {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9999;
  overflow: hidden;
}

.loading-area .loading-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 1;
  z-index: 9999;
}

.loading-area .loading-pic {
  width: 100%;
  position: absolute;
  top: 50%;
  z-index: 99999;
  text-align: center;
  transform: translateY(-50%);
}

/*Loading Animation Start*/
#outer-barG {
  height: 31px;
  width: 109px;
  border: 2px solid #1e8fd0;
  overflow: hidden;
  background-color: white;
  margin: auto;
}

#outer-barG .bar-lineG {
  background-color: #ff8800;
  float: left;
  width: 15px;
  height: 102px;
  margin-right: 20px;
  margin-top: -24px;
  transform: rotate(45deg);
}

#outer-barG .bar-animationG {
  margin-left: 143px;
  width: 143px;
  animation-name: bar-animationG;
  animation-duration: 2.24s;
  animation-iteration-count: infinite;
  animation-direction: normal;
}

@keyframes bar-animationG {
  0% {
    margin-left: 132px;
    margin-top: -15px;
  }
  100% {
    margin-left: -109px;
    margin-top: -15px;
  }
}

/*Description list*/
.description-list {
  margin: 30px 0px;
}

.description-list li {
  padding-left: 25px;
  list-style: none;
  position: relative;
  margin-bottom: 10px;
}

.description-list li:last-child {
  margin-bottom: 0px;
}

.description-list li i {
  color: #ff8a00;
  line-height: 26px;
  position: absolute;
  left: 0px;
  top: 0px;
  text-align: center;
  font-size: 22px;
}

/*Section Overlay*/
.overlay-wraper {
  position: relative;
}

.overlay-wraper .overlay-main {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.opacity-01 {
  opacity: 0.1;
}

.opacity-02 {
  opacity: 0.2;
}

.opacity-03 {
  opacity: 0.3;
}

.opacity-04 {
  opacity: 0.4;
}

.opacity-05 {
  opacity: 0.5;
}

.opacity-06 {
  opacity: 0.6;
}

.opacity-07 {
  opacity: 0.7;
}

.opacity-08 {
  opacity: 0.8;
}

.opacity-09 {
  opacity: 0.9;
}

.overlay-wraper > .container {
  position: relative;
  z-index: 1;
}

.overlay-wraper > .container-fluid {
  position: relative;
  z-index: 1;
}

/*What We do*/
@media (max-width: 991px) {
  .tw-what-wedo-media {
    text-align: center;
    margin-bottom: 30px;
  }
}

.tw-service-icon-box-wrap {
  position: relative;
  padding: 30px 90px 30px 40px;
  margin-top: 50px;
  z-index: 1;
  margin-bottom: 80px;
}

@media (max-width: 991px) {
  .tw-service-icon-box-wrap {
    padding-left: 10px;
  }
}

@media (max-width: 767px) {
  .tw-service-icon-box-wrap {
    margin-bottom: 50px;
  }
}

@media (max-width: 480px) {
  .tw-service-icon-box-wrap {
    padding: 0px 30px 30px 10px;
    margin-bottom: 30px;
  }
}

.tw-service-icon-box-wrap:after {
  position: absolute;
  content: '';
  top: -50px;
  bottom: -50px;
  right: 0px;
  border-style: solid;
  border-color: #ff8a00;
  border-width: 60px 60px 60px 0px;
  left: 25%;
  z-index: -1;
}

@media (max-width: 480px) {
  .tw-service-icon-box-wrap:after {
    border-width: 30px 30px 30px 0px;
    bottom: 0px;
    top: -30px;
  }
}

.service-icon-box-two {
  position: relative;
  margin-bottom: 5px;
  padding: 20px;
  background-color: #fff;
  transition: all 0.3s linear;
  z-index: 0;
}

.service-icon-box-two:before {
  width: 8px;
  content: '';
  position: absolute;
  z-index: 1;
  opacity: 0.5;
  left: -8px;
  top: 30px;
  bottom: 30px;
  background-color: #ff8a00;
  transition: all 0.3s linear;
}

.service-icon-box-two:hover {
  box-shadow: 0px 0px 50px rgba(30, 143, 208, 0.5);
  z-index: 999;
}

.service-icon-box-two:hover:before {
  left: 0%;
  opacity: 1;
}

.service-icon-box-two .service-icon-box-two-media {
  margin-right: 30px;
  margin-top: 20px;
  float: left;
  width: 70px;
}

@media (max-width: 400px) {
  .service-icon-box-two .service-icon-box-two-media {
    margin-right: 20px;
    width: 40px;
  }
}

.service-icon-box-two .service-icon-box-title {
  overflow: hidden;
}

.service-icon-box-two .service-icon-box-title .wt-title {
  margin-bottom: 15px;
}

.service-icon-box-two .service-icon-box-title .wt-title span {
  font-size: 32px;
  padding-right: 5px;
}

@media (max-width: 767px) {
  .service-icon-box-two .service-icon-box-title .wt-title {
    padding-right: 0px;
  }
}

.service-icon-box-two .service-icon-box-title p {
  margin-bottom: 0px;
}

.service-icon-box-two.site-bg-black .service-icon-box-title .wt-title a {
  color: #fff;
}

/*Blog post 4*/
.blog-post-4-outer .wt-post-media img {
  width: 100%;
}

.blog-post-4-outer .wt-post-info {
  padding-top: 50px;
  position: relative;
  z-index: 1;
}

.blog-post-4-outer .wt-post-info .wt-post-meta {
  position: relative;
}

.blog-post-4-outer .wt-post-info .wt-post-meta ul {
  list-style: none;
  margin-bottom: 0px;
}

.blog-post-4-outer .wt-post-info .wt-post-meta ul li {
  position: relative;
  z-index: 1;
}

.blog-post-4-outer .wt-post-info .wt-post-meta ul li.post-date {
  background-color: #0b2f44;
  color: #fff;
  display: inline-table;
  font-size: 22px;
  line-height: 28px;
  text-align: center;
  padding: 20px 15px;
  font-weight: 600;
  position: absolute;
  right: 30px;
  bottom: 20px;
  width: 90px;
  font-family: "Oswald", sans-serif;
}

.blog-post-4-outer .wt-post-info .wt-post-meta ul li.post-date:before {
  position: absolute;
  content: '';
  border-left: 8px solid #ff8a00;
  border-bottom: 8px solid #ff8a00;
  top: 8px;
  left: -8px;
  bottom: -8px;
  width: 100%;
}

.blog-post-4-outer .wt-post-info .wt-post-meta ul li.post-date span {
  font-size: 40px;
  color: #ff8a00;
  font-weight: 700;
  display: inline-table;
  line-height: 0.75;
}

.blog-post-4-outer .wt-post-info .wt-post-meta ul li.post-category {
  font-size: 14px;
  font-weight: 600;
  color: #ff8a00;
}

.blog-post-4-outer .wt-post-info .wt-post-meta-list {
  text-align: left;
  font-size: 15px;
  line-height: 21px;
  margin-bottom: 20px;
  font-weight: 600;
  color: #000;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.blog-post-4-outer .wt-post-info .wt-post-meta-list .wt-list-content {
  display: inline-block;
}

.blog-post-4-outer .wt-post-info .wt-post-meta-list .wt-list-content a {
  padding: 0px 3px;
}

.blog-post-4-outer .wt-post-info .wt-post-meta-list .wt-list-content a:hover {
  color: #ff8a00;
}

.blog-post-4-outer .wt-post-info .wt-post-title .post-title {
  margin-bottom: 15px;
}

.blog-no-image {
  background-color: #ff8a00;
  padding: 30px;
  color: #fff;
  position: relative;
  z-index: 1;
}

.blog-no-image .wt-post-info {
  display: flex;
  margin-bottom: 15px;
}

.blog-no-image .wt-post-info .post-date {
  color: #0b2f44;
  display: inline-table;
  padding-top: 4px;
  font-size: 22px;
  line-height: 28px;
  text-align: center;
  padding-right: 20px;
  font-weight: 600;
  width: 90px;
  font-family: "Oswald", sans-serif;
}

.blog-no-image .wt-post-info .post-date span {
  font-size: 40px;
  font-weight: 700;
  line-height: 0.75;
}

.blog-no-image .wt-post-info .post-title {
  margin-bottom: 0px;
}

.blog-no-image .wt-post-info .post-title a {
  color: #fff;
}

.blog-no-image .qt-light {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: -1;
  opacity: 0.1;
}

/*-----All Services------*/
.all_services {
  border: 1px solid #ced4da;
  padding: 30px;
}

.all_services ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.all_services ul li {
  position: relative;
  margin-bottom: 12px;
}

.all_services ul li:last-child {
  margin-bottom: 0px;
}

.all_services ul li a {
  transition: 0.5s all ease;
  position: relative;
  padding-left: 35px;
  display: block;
  color: #0b2f44;
  font-family: "Oswald", sans-serif;
  font-size: 18px;
}

.all_services ul li a:after {
  content: "\f04b";
  position: absolute;
  left: 0px;
  top: 8px;
  font-size: 10px;
  font-weight: 700;
  font-family: 'FontAwesome';
}

.all_services ul li a:hover {
  color: #ff8a00;
}

.all_services ul li a.active {
  color: #ff8a00;
}

.service-full-info .service-category-title {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
}

.service-full-info .service-category-title .service-category-media {
  width: 100px;
  display: inline-block;
}

.service-full-info .service-category-title h2 {
  display: inline-block;
}

@media (max-width: 991px) {
  .service-full-info .service-category-title h2 {
    font-size: 30px;
  }
}

.service-full-info .wt-title {
  font-size: 36px;
}

@media (max-width: 991px) {
  .service-full-info .wt-title {
    font-size: 28px;
  }
}

.service-offered {
  display: flex;
}

.service-offered .media {
  flex: 1.5;
}

.service-offered .service-offered-list {
  flex: 1;
}

@media (max-width: 1199px) {
  .service-offered {
    display: block;
  }
  .service-offered .media {
    margin-bottom: 30px;
  }
}

.tw-checked-list ul {
  list-style: none;
  margin-bottom: 0px;
}

.tw-checked-list ul li {
  position: relative;
  font-size: 16px;
  margin-bottom: 3px;
  padding-left: 20px;
}

.tw-checked-list ul li:after {
  content: '\f105';
  font-family: 'FontAwesome';
  color: #ff8a00;
  left: 0px;
  top: 0px;
  position: absolute;
}

#search {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-color: #fff;
  transition: all 0.5s ease-in-out;
  transform: translate(0px, -100%) scale(0, 0);
  opacity: 0;
  display: none;
}

#search.open {
  transform: translate(0px, 0px) scale(1, 1);
  opacity: 1;
  z-index: 999;
  display: block;
}

#search form {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 500px;
  width: 100%;
  color: #fff;
  border: 1px solid #ff8a00;
  font-size: 30px;
  font-weight: 300;
  text-align: left;
  outline: none;
  padding: 10px;
  border-radius: 0px;
}

#search form span {
  display: block;
}

#search input[type="search"] {
  background: none;
  border: none;
  padding: 0px 12px;
  outline: none;
  color: #0b2f44;
  font-size: 24px;
  text-align: center;
}

@media (max-width: 575px) {
  #search input[type="search"] {
    font-size: 14px;
  }
}

#search input[type="search"]:focus {
  box-shadow: none;
}

#search input[type="search"]::placeholder {
  color: #0b2f44;
}

#search .search-btn {
  border: none;
  background: none;
  padding: 0px 30px;
  outline: none;
  display: block;
  background-color: #ff8a00;
  width: 100%;
  text-align: center;
}

#search .search-btn i {
  font-size: 36px;
  color: #fff;
}

#search .close {
  position: fixed;
  top: 30px;
  right: 30px;
  opacity: 1;
  font-size: 27px;
  color: #fff;
  width: 60px;
  height: 60px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  background-color: #ff8a00;
}

#search .close:after, #search .close:before {
  content: "";
  position: absolute;
  width: 2px;
  height: 32px;
  background-color: #fff;
  right: 28px;
  top: 16px;
}

#search .close:after {
  transform: rotate(45deg);
}

#search .close:before {
  transform: rotate(-45deg);
}

#search .close:hover:after, #search .close:hover:before {
  cursor: pointer;
}

/*services-box-one*/
.tw-service-gallery-style1-area {
  position: relative;
}

@media (max-width: 1440px) {
  .services-gallery-block-outer {
    max-width: calc(100% - 170px);
  }
}

@media (max-width: 1200px) {
  .services-gallery-block-outer {
    max-width: 100%;
  }
}

@media (max-width: 991px) {
  .services-gallery-block-outer {
    padding: 38px 15px;
    max-width: 720px;
    margin: 0px auto;
  }
}

.tyre-mark-bg {
  position: relative;
  z-index: 1;
}

.tyre-mark-bg:before {
  content: '';
  position: absolute;
  left: 0px;
  bottom: 0px;
  height: 100%;
  width: 100%;
  z-index: -1;
  background-image: url(../images/tyre-mark.png);
  background-size: auto;
  background-repeat: no-repeat;
  background-position: left bottom;
}

/*Hilite Text*/
.tw-hilite-text-wrap {
  position: relative;
}

.tw-hilite-text-wrap .tw-hilite-text {
  position: absolute;
}

.tw-hilite-text-wrap .tw-hilite-text.right {
  bottom: -80px;
  right: 20px;
}

.tw-hilite-text-wrap .tw-hilite-text span {
  display: inline-table;
  font-family: "Oswald", sans-serif;
  font-size: 120px;
  line-height: 0.75;
  font-weight: 700;
  color: #000;
  text-transform: uppercase;
  opacity: 0.05;
}

@media (max-width: 991px) {
  .tw-hilite-text-wrap .tw-hilite-text span {
    display: none;
  }
}

.tw-hilite-text-wrap2 {
  position: relative;
}

.tw-hilite-text-wrap2 .tw-hilite-text span {
  display: inline-table;
  font-family: "Oswald", sans-serif;
  font-size: 120px;
  line-height: 0.75;
  font-weight: 700;
  color: #000;
  text-transform: uppercase;
  opacity: 0.05;
}

@media (max-width: 991px) {
  .tw-hilite-text-wrap2 .tw-hilite-text span {
    display: none;
  }
}

.services-gallery-style1 {
  margin-bottom: 30px;
}

@media (max-width: 1200px) {
  .services-gallery-style1 {
    padding-left: 0px;
  }
}

@media (max-width: 991px) {
  .services-gallery-style1 {
    margin-bottom: 10px;
  }
}

.services-gallery-style1 .owl-carousel .owl-stage-outer {
  position: relative;
  margin-bottom: 30px;
}

.services-gallery-style1 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-next {
  right: -1px;
  transition: all 0.2s linear;
}

.services-gallery-style1 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-prev {
  transition: all 0.2s linear;
}

.services-gallery-style1 .services-gallery-one.owl-btn-bottom-left .owl-nav {
  opacity: 1;
  position: absolute;
  left: 0px;
  bottom: 0px;
}

@media (max-width: 991px) {
  .services-gallery-style1 .services-gallery-one.owl-btn-bottom-left .owl-nav {
    text-align: center;
    position: inherit;
    margin-top: 20px;
    bottom: 20px;
  }
  .services-gallery-style1 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-prev {
    margin-right: 14px;
  }
  .services-gallery-style1 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-next {
    margin-right: 0px;
  }
}

.service-box-style1 {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.service-box-style1 .service-content {
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  position: absolute;
  width: 100%;
  z-index: 1;
  transition: all 0.8s linear;
}

.service-box-style1 .service-content .service-content-inner {
  padding: 20px;
  margin: 20px;
  transition: all 0.8s linear;
  background-color: rgba(11, 47, 68, 0.9);
  position: relative;
  display: table;
  height: 0%;
}

.service-box-style1 .service-content .service-content-inner .service-content-top {
  display: table-header-group;
}

.service-box-style1 .service-content .service-content-inner .service-content-top .service-title-large {
  text-align: right;
}

.service-box-style1 .service-content .service-content-inner .service-content-top .service-title-large a {
  color: #fff;
  transition: all 0.8s linear;
  text-transform: uppercase;
  font-size: 32px;
  line-height: 0.7;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .service-box-style1 .service-content .service-content-inner .service-content-top .service-title-large a {
    font-size: 26px;
  }
}

.service-box-style1 .service-content .service-content-inner .service-content-bottom {
  display: table-cell;
  vertical-align: bottom;
}

.service-box-style1 .service-content .service-content-inner .service-content-bottom .service-title-large-number {
  font-size: 40px;
  color: #fff;
  transition: all 0.8s linear;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .service-box-style1 .service-content .service-content-inner .service-content-bottom .service-title-large-number {
    font-size: 30px;
  }
}

.service-box-style1 .service-content .service-content-inner .service-content-bottom p {
  color: #fff;
  transition: all 0.8s linear;
  margin-bottom: 20px;
}

.service-box-style1 .service-content .service-content-inner .service-content-bottom .site-button-2 {
  transition: all 0.8s linear;
}

.service-box-style1:hover .service-content {
  bottom: 40px;
  height: calc(100% - 40px);
}

.service-box-style1:hover .service-content .service-content-inner {
  height: 100%;
  background-color: rgba(255, 138, 0, 0.9);
}

.service-box-style1:hover .service-content .service-content-inner .service-content-top .service-title-large a {
  color: #0b2f44;
}

.service-box-style1:hover .service-content .service-content-inner .service-content-bottom .service-title-large-number {
  color: #0b2f44;
}

.service-box-style1:hover .service-content .service-content-inner .service-content-bottom p {
  color: #0b2f44;
}

.service-box-style1:hover .service-content .service-content-inner .service-content-bottom .site-button-2 {
  color: #0b2f44;
}

.service-box-style1 .service-media img {
  width: 100%;
}

@media (max-width: 991px) {
  .service-box-style1 .service-media img {
    height: 420px;
    object-fit: cover;
  }
}

/*site-button-verticle*/
.site-button-2 {
  display: inline-block;
  color: #fff;
  font-size: 18px;
  text-transform: uppercase;
  transition: 0.5s all ease;
  position: relative;
  overflow: hidden;
}

.site-button-2:after {
  content: '\f105';
  position: absolute;
  font-family: 'FontAwesome';
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0px;
  transition: 0.5s all ease;
  opacity: 0;
}

.site-button-2:hover {
  padding-left: 20px;
  color: #ff8a00;
}

.site-button-2:hover:after {
  font-size: 24px;
  left: 0px;
  opacity: 1;
}

.tw-project-1-wrap.tw-project-1-wrap-bg {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.tw-project-1-wrap.tw-project-1-wrap-bg:before {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100%;
  z-index: -1;
  background-image: url(../images/project/project-bg.jpg);
  background-size: auto;
  background-repeat: no-repeat;
  background-position: left top;
}

.tw-project-1-wrap .tw-project-1-content {
  position: relative;
  margin-left: 30%;
  margin-bottom: 120px;
  padding-top: 90px;
}

@media (max-width: 1366px) {
  .tw-project-1-wrap .tw-project-1-content {
    margin-left: 24%;
  }
}

@media (max-width: 991px) {
  .tw-project-1-wrap .tw-project-1-content {
    margin-left: 0px;
  }
}

.tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position {
  max-width: 1038px;
  top: 45px;
  padding: 65px 48px;
  box-shadow: 0px 0px 50px rgba(30, 143, 208, 0.5);
  position: relative;
  z-index: 1;
}

@media (max-width: 1366px) {
  .tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position {
    margin-right: 50px;
  }
}

@media (max-width: 991px) {
  .tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position {
    margin-right: 0px;
  }
}

@media (max-width: 420px) {
  .tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position {
    padding: 20px;
  }
}

.tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position:before {
  content: '';
  position: absolute;
  left: 48px;
  top: -75px;
  right: -75px;
  bottom: -75px;
  border: 20px solid #1e8fd0;
  z-index: -1;
}

@media (max-width: 1366px) {
  .tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position:before {
    right: -50px;
  }
}

@media (max-width: 991px) {
  .tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position:before {
    display: none;
  }
}

.tw-project-1-wrap .tw-project-1-content .tw-project-1-content-position:after {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  background-color: #fff;
  z-index: -1;
}

.project-carousel .owl-stage-outer {
  padding-bottom: 0px;
}

.project-new-2 {
  position: relative;
  margin-bottom: 50px;
}

.project-new-2 .wt-img-effect {
  position: relative;
}

.project-new-2 .wt-img-effect img {
  width: 100%;
}

.project-new-2 .project-new-content {
  padding: 22px 30px 30px 30px;
  position: absolute;
  background-color: #fff;
  left: 30px;
  bottom: -50px;
  right: 0px;
}

.project-new-2 .project-new-content span {
  display: inline-block;
  color: #ff8a00;
  font-size: 14px;
  font-weight: 400;
}

.project-new-2 .project-new-content .wt-title {
  margin-bottom: 0px;
}

.project-new-2 .project-new-content .wt-title a {
  transition: 0.5s all ease;
}

.project-new-2 .project-new-content .wt-title a:hover {
  color: #ff8a00;
}

.project-new-2 .project-new-content .site-button-h-align {
  position: absolute;
  font-size: 12px;
  color: #0b2f44;
  text-transform: uppercase;
  font-weight: 600;
  transform: rotate(-90deg) translateX(-50%);
  left: 14px;
  top: 50%;
  transform-origin: 0;
  margin-top: -15px;
  transition: 0.5s all ease;
}

.project-new-2 .project-new-content .site-button-h-align:before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  width: 15px;
  height: 1px;
  background-color: #ff8a00;
  margin-right: 4px;
}

.project-new-2 .project-new-content .site-button-h-align:hover {
  color: #ff8a00;
}

.project-new-2 .project-view {
  position: absolute;
  top: 30px;
  left: 30px;
  opacity: 1;
  transform: scaleY(0);
  transition: transform 0.5s cubic-bezier(0.57, 0.04, 0.06, 0.84), opacity 0s 0.5s;
}

.project-new-2 .project-view .project-view-btn {
  position: relative;
  display: block;
  width: 60px;
  height: 60px;
}

.project-new-2 .project-view .project-view-btn i {
  width: 60px;
  height: 60px;
  font-size: 28px;
  line-height: 60px;
  display: block;
  text-align: center;
  background-color: #ff8a00;
  position: relative;
  color: #fff;
}

.project-new-2:hover .project-view {
  opacity: 1;
  z-index: 3;
  transform: scaleY(1);
}

.project-new-2:hover .wt-img-effect:after, .project-new-2:hover .wt-img-effect:before {
  width: 100%;
}

/*touchspin input type number*/
.bootstrap-touchspin .input-group-btn-vertical {
  position: relative;
  white-space: nowrap;
  width: 1%;
  vertical-align: middle;
  display: table-cell;
}

.bootstrap-touchspin .input-group-btn-vertical > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
  padding: 10px;
  margin-left: -1px;
  position: relative;
  border-width: 1px;
  border-style: solid;
  background: #ff8a00;
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up {
  border-radius: 0;
  border-top-right-radius: 0;
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down {
  margin-top: -2px;
  border-radius: 0;
  border-bottom-right-radius: 0;
}

.bootstrap-touchspin .input-group-btn-vertical i {
  position: absolute;
  top: 7px;
  left: 7px;
  font-size: 9px;
  font-weight: normal;
  color: #fff;
}

.tw-contact-bg-section .tw-contact-bg-inner {
  padding: 50px 40px;
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
  background-size: cover;
  background-position: center center;
}

.tw-contact-bg-section .tw-contact-bg-inner:after {
  content: '';
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  position: absolute;
  z-index: -1;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 33%, rgba(255, 138, 0, 0.65) 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#a6ff8a00',GradientType=0 );
  /* IE6-9 */
}

.tw-contact-bg-section .tw-contact-bg-inner:before {
  content: '';
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: -1;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-top {
  margin-bottom: 50px;
  text-align: center;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-top span {
  display: block;
  color: #fff;
  font-size: 18px;
  font-family: "Oswald", sans-serif;
  position: relative;
  margin-bottom: 20px;
  padding-bottom: 10px;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-top span:after {
  position: absolute;
  background: #1e8fd0;
  width: 60px;
  height: 5px;
  content: '';
  left: 50%;
  bottom: -5px;
  transform: translateX(-50%);
}

.tw-contact-bg-section .tw-contact-bg-inner .section-top .tw-con-number {
  font-size: 36px;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-top .tw-con-number a {
  color: #fff;
}

@media (max-width: 1199px) {
  .tw-contact-bg-section .tw-contact-bg-inner .section-top .tw-con-number {
    font-size: 26px;
  }
}

@media (max-width: 420px) {
  .tw-contact-bg-section .tw-contact-bg-inner .section-top .tw-con-number {
    font-size: 26px;
  }
}

.tw-contact-bg-section .tw-contact-bg-inner .section-bot ul {
  margin: 0px;
  list-style: none;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-bot ul li {
  color: #fff;
  text-align: center;
  margin-bottom: 20px;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-bot ul li span {
  width: 46px;
  height: 46px;
  line-height: 46px;
  background-color: #fff;
  display: block;
  border-radius: 50%;
  text-align: center;
  margin: 0px auto 8px;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-bot ul li p {
  margin-bottom: 0px;
}

.tw-contact-bg-section .tw-contact-bg-inner .section-bot ul li:last-child {
  margin-bottom: 0px;
}

/*Google map*/
.google-map {
  width: 100%;
  height: 460px;
}

.google-map iframe {
  width: 100%;
  border: 0px;
  filter: grayscale(100%);
}

/*Text with bg image*/
.tw-company-years {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

@media (max-width: 575px) {
  .tw-company-years {
    display: block;
  }
}

.tw-company-years .light {
  position: relative;
  margin-right: 60px;
  display: inline-flex;
}

.tw-company-years .light h1 {
  font-size: 200px;
  line-height: 0.75;
  display: inline-table;
  color: #0b2f44;
  margin: 0px;
}

@media (max-width: 991px) {
  .tw-company-years .light {
    margin-right: 20px;
  }
  .tw-company-years .light h1 {
    font-size: 100px;
  }
}

.tw-company-years img {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  mix-blend-mode: lighten;
  transform: translateY(-50%);
}

.tw-company-years .tw-company-info {
  max-width: 500px;
}

.tw-company-years .tw-company-info span {
  font-size: 36px;
  line-height: 36px;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
  color: #0b2f44;
}

@media (max-width: 991px) {
  .tw-company-years .tw-company-info span {
    font-size: 30px;
  }
}

@media (max-width: 575px) {
  .tw-company-years .tw-company-info span {
    font-size: 26px;
  }
}

/* Half effect button */
.btn-half {
  cursor: pointer;
  background-color: #ff8a00;
  z-index: 0;
  display: inline-block;
  position: relative;
  overflow: visible;
}

.btn-half:after {
  position: absolute;
  right: -6px;
  top: -6px;
  bottom: -6px;
  content: '';
  width: 20px;
  border: 2px solid #ff8a00;
  transition: 0.5s all ease;
}

.btn-half span {
  color: #fff;
  display: block;
  padding-left: 0%;
  padding-right: 35px;
  font-size: 18px;
  line-height: 24px;
  font-family: "Rubik", sans-serif;
  font-weight: 400;
  transform: scaleX(0.9);
  transform-origin: center left;
  transition: color 0.3s ease;
  position: relative;
  z-index: 1;
}

@media (max-width: 480px) {
  .btn-half span {
    font-size: 14px;
  }
}

.btn-half em {
  position: absolute;
  height: 1px;
  background-color: #fff;
  width: 26px;
  right: 30px;
  top: 50%;
  transition: all 0.3s ease;
  z-index: 1;
}

@media (max-width: 480px) {
  .btn-half em {
    right: 15px;
  }
}

.btn-half:hover em {
  width: 40px;
}

.btn-half:hover:after {
  width: 40px;
}

/*Why Choose Section*/
.tw-why-choose-area-top {
  background-position: bottom right;
}

@media (max-width: 767px) {
  .tw-why-choose-area-top {
    background-size: auto;
  }
}

.tw-why-choose-area {
  padding-bottom: 100px;
}

@media (max-width: 991px) {
  .tw-why-choose-area {
    padding-bottom: 40px;
  }
}

.tw-why-choose-section .row {
  align-items: center;
  padding-bottom: 90px;
}

@media (max-width: 991px) {
  .tw-why-choose-section .row {
    padding-bottom: 0px;
  }
}

@media (max-width: 991px) {
  .tw-why-choose-section .tw-why-choose-left {
    margin-bottom: 50px;
  }
}

@media (max-width: 480px) {
  .tw-why-choose-section .tw-why-choose-left {
    margin-bottom: 40px;
  }
}

.tw-why-choose-section .tw-why-choose-left strong {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
  margin-bottom: 20px;
  display: block;
}

.tw-why-choose-section .tw-why-choose-left .tw-why-choose-left-bottom {
  padding-top: 30px;
  display: flex;
}

@media (max-width: 480px) {
  .tw-why-choose-section .tw-why-choose-left .tw-why-choose-left-bottom {
    display: block;
  }
}

.tw-why-choose-section .tw-why-choose-left .tw-why-choose-left-bottom .site-button {
  margin-right: 30px;
}

.tw-why-choose-section .tw-why-choose-left .tw-why-choose-left-bottom .sign-font {
  position: relative;
}

.tw-why-choose-section .tw-why-choose-left .tw-why-choose-left-bottom .sign-font span {
  font-family: 'Sacramento', cursive;
  font-size: 45px;
  color: #000;
  position: absolute;
  left: 0px;
  top: 0px;
  transform: rotate(-20deg);
}

@media (max-width: 480px) {
  .tw-why-choose-section .tw-why-choose-left .tw-why-choose-left-bottom .sign-font span {
    position: relative;
  }
}

.tw-why-choose-section .tw-why-choose-right {
  position: relative;
}

@media (max-width: 991px) {
  .tw-why-choose-section .tw-why-choose-right {
    margin-bottom: 30px;
  }
}

.tw-why-choose-section .tw-why-choose-right .tw-why-choose-media1 {
  position: relative;
  max-width: 338px;
  padding-top: 160px;
}

@media (max-width: 480px) {
  .tw-why-choose-section .tw-why-choose-right .tw-why-choose-media1 {
    padding-top: 0px;
    max-width: 100%;
    margin-bottom: 40px;
  }
}

.tw-why-choose-section .tw-why-choose-right .tw-why-choose-media1 img {
  width: 100%;
}

.tw-why-choose-section .tw-why-choose-right .tw-why-choose-media2 {
  position: absolute;
  max-width: 338px;
  right: 0px;
  top: 0px;
  z-index: 2;
}

@media (max-width: 480px) {
  .tw-why-choose-section .tw-why-choose-right .tw-why-choose-media2 {
    position: inherit;
    max-width: 100%;
    margin-left: 20px;
  }
  .tw-why-choose-section .tw-why-choose-right .tw-why-choose-media2 img {
    width: 100%;
  }
}

.tw-why-choose-section .tw-why-choose-right .tw-why-choose-media2:after {
  position: absolute;
  content: '';
  border-left: 40px solid #fff;
  border-bottom: 40px solid #fff;
  left: -40px;
  bottom: -40px;
  height: 100%;
  width: 100%;
  z-index: -1;
  box-shadow: 0px 40px 60px rgba(30, 143, 208, 0.7);
}

@media (max-width: 480px) {
  .tw-why-choose-section .tw-why-choose-right .tw-why-choose-media2:after {
    border-left: 20px solid #fff;
    border-bottom: 20px solid #fff;
    left: -20px;
    bottom: -20px;
  }
}

.tw-any-help-section .container {
  position: relative;
}

.tw-any-help-section .container .tw-any-help-inner {
  background-color: #fff;
  padding: 30px;
  position: absolute;
  z-index: 2;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0px 20px 40px rgba(255, 138, 0, 0.4);
}

.tw-any-help-section .container .tw-any-help-inner img {
  position: absolute;
  left: -150px;
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 991px) {
  .tw-any-help-section .container .tw-any-help-inner img {
    position: inherit;
    left: auto;
    top: auto;
    transform: none;
    margin-bottom: 20px;
  }
}

.tw-any-help-section .container .tw-any-help-inner span {
  display: block;
}

.tw-any-help-section .container .tw-any-help-inner span.tw-24 {
  text-transform: uppercase;
  color: #0b2f44;
}

.tw-any-help-section .container .tw-any-help-inner span.tw-help-title {
  font-size: 36px;
  font-weight: 700;
  color: #ff8a00;
  font-family: "Oswald", sans-serif;
}

.tw-any-help-section .container .tw-any-help-inner span.tw-help-number {
  font-size: 32px;
  font-weight: 500;
  color: #0b2f44;
}

@media (max-width: 991px) {
  .tw-any-help-section .container .tw-any-help-inner span.tw-help-number {
    font-size: 20px;
  }
  .tw-any-help-section .container .tw-any-help-inner span.tw-help-title {
    font-size: 26px;
  }
}

@media (max-width: 991px) {
  .tw-any-help-section .container .tw-any-help-inner {
    position: inherit;
    transform: none;
    margin-top: 60px;
  }
}

/*Estimation Section*/
@media (max-width: 991px) {
  .tw-estimation-area {
    padding-bottom: 0px !important;
  }
}

.tw-estimation-section .row {
  justify-content: center;
}

.tw-est-section-block {
  height: 700px;
  position: relative;
  z-index: 1;
  border: 15px solid transparent;
  transition: 0.5s all ease;
}

@media (max-width: 991px) {
  .tw-est-section-block {
    height: auto;
  }
}

.tw-est-section-block .tw-est-section-block-content {
  position: absolute;
  left: 0px;
  bottom: 0px;
  color: #fff;
  padding: 10px;
  transform: translate3d(0%, 0, 0);
  transition: opacity 0.35s, transform 0.8s;
}

@media (max-width: 991px) {
  .tw-est-section-block .tw-est-section-block-content {
    position: relative;
  }
}

.tw-est-section-block .tw-est-section-block-content .tw-est-section-number {
  font-size: 80px;
  font-family: "Oswald", sans-serif;
  font-weight: 700;
}

@media (max-width: 991px) {
  .tw-est-section-block .tw-est-section-block-content .tw-est-section-number {
    font-size: 40px;
  }
}

.tw-est-section-block .tw-est-section-block-content .tw-title {
  color: #fff;
}

.tw-est-section-block:hover {
  border: 15px solid rgba(255, 255, 255, 0.5);
  background-color: rgba(0, 0, 0, 0.4);
}

.tw-est-section-block:hover .tw-est-section-block-content {
  transform: translate3d(0%, -100px, 0);
}

@media (max-width: 991px) {
  .tw-est-section-block:hover .tw-est-section-block-content {
    transform: translate3d(0%, -20px, 0);
  }
}

.site-button-2-outline {
  border: 2px solid #fff;
  padding: 10px 16px;
  display: inline-block;
}

.site-button-2-outline i {
  color: #fff;
  font-size: 24px;
  line-height: 24px;
  display: block;
  position: relative;
  padding-left: 18px;
}

.site-button-2-outline i:after {
  content: '';
  position: absolute;
  width: 25px;
  height: 2px;
  background-color: #fff;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  margin-top: 1px;
}

.site-button-2-outline.dark {
  border: 2px solid #0b2f44;
}

.site-button-2-outline.dark i {
  color: #0b2f44;
}

.site-button-2-outline.dark i:after {
  background-color: #0b2f44;
}

/*Testimonials 1*/
.slider-vertical-wrap {
  position: relative;
  margin-left: 20px;
  padding: 170px 0px;
  max-width: 540px;
  z-index: 1;
}

@media (max-width: 1199px) {
  .slider-vertical-wrap {
    max-width: calc(100% - 80px);
    margin-left: 0px;
  }
}

@media (max-width: 480px) {
  .slider-vertical-wrap {
    max-width: calc(100%);
    padding: 40px 0px;
  }
}

.slider-vertical-wrap .slick-prev,
.slider-vertical-wrap .slick-next {
  top: auto;
  bottom: -30px;
  width: 30px;
  height: 30px;
}

@media (max-width: 480px) {
  .slider-vertical-wrap .slick-prev,
  .slider-vertical-wrap .slick-next {
    bottom: -45px;
  }
}

.slider-vertical-wrap .slick-prev:before,
.slider-vertical-wrap .slick-next:before {
  color: #fff;
  font-family: 'FontAwesome';
  background-color: #0b2f44;
  opacity: 1;
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: block;
}

.slider-vertical-wrap .slick-prev {
  left: 4px;
}

.slider-vertical-wrap .slick-prev:before {
  content: "\f106";
}

@media (max-width: 480px) {
  .slider-vertical-wrap .slick-prev:before {
    content: "\f104";
  }
}

.slider-vertical-wrap .slick-next {
  left: 56px;
}

.slider-vertical-wrap .slick-next:before {
  content: "\f107";
}

@media (max-width: 480px) {
  .slider-vertical-wrap .slick-next:before {
    content: "\f105";
  }
}

.tw-testimonial-1-area {
  overflow: hidden;
}

.tw-testimonials1-cell {
  text-align: left;
  padding-left: 130px;
}

@media (max-width: 1270px) {
  .tw-testimonials1-cell {
    padding-right: 30px;
  }
}

@media (max-width: 480px) {
  .tw-testimonials1-cell {
    padding-right: 0px;
    padding-left: 0px;
  }
}

.tw-testimonials1-cell .tw-testimonials1-text {
  margin-bottom: 40px;
  position: relative;
  font-size: 14px;
  font-style: italic;
}

@media (max-width: 480px) {
  .tw-testimonials1-cell .tw-testimonials1-text {
    margin-bottom: 15px;
  }
}

.tw-testimonials1-cell .tw-testimonials1-text .tw-testimonials1-quote {
  display: block;
  margin-bottom: 30px;
}

@media (max-width: 480px) {
  .tw-testimonials1-cell .tw-testimonials1-text .tw-testimonials1-quote {
    margin-bottom: 15px;
  }
}

.tw-testimonials1-cell .tw-testimonials1-name {
  font-size: 30px;
  line-height: 38px;
  font-family: 'Sacramento', cursive;
  color: #0b2f44;
  font-weight: 600;
}

.tw-testimonials1-cell .tw-testimonials1-postion {
  font-size: 14px;
  color: #ff8a00;
  position: relative;
  padding-left: 30px;
}

.tw-testimonials1-cell .tw-testimonials1-postion:before {
  content: '';
  position: absolute;
  left: 0px;
  top: 50%;
  width: 20px;
  height: 1px;
  background-color: #0b2f44;
  transform: translateY(-50%);
}

.slick-testimonials-thumbnails {
  width: 105px;
  position: absolute;
  left: 0px;
  top: 50%;
  z-index: 1;
  transform: translateY(-50%);
}

@media (max-width: 480px) {
  .slick-testimonials-thumbnails {
    width: 100%;
    position: inherit;
    top: auto;
    transform: none;
    left: auto;
  }
}

.slick-testimonials-thumbnails .slick-list {
  padding: 0px !important;
}

.slick-testimonials-thumbnails .slick-arrow {
  display: none !important;
}

.slick-testimonials-thumbnails .slick-active.slick-center .slick-testimonials-thumb:before {
  opacity: 1;
}

@media (max-width: 480px) {
  .slick-testimonials-thumbnails .slick-active.slick-center .slick-testimonials-thumb img {
    border-color: #ff8a00;
  }
  .slick-testimonials-thumbnails .slick-active.slick-center .slick-testimonials-thumb:before {
    display: none;
  }
}

.slick-testimonials-thumb {
  cursor: pointer;
  margin-bottom: 15px;
  padding-right: 15px;
  transition: 0.5s all ease;
  position: relative;
  z-index: 1;
}

@media (max-width: 480px) {
  .slick-testimonials-thumb {
    padding: 0px;
  }
}

.slick-testimonials-thumb:before {
  content: '';
  width: 50px;
  height: 50px;
  position: absolute;
  right: 4px;
  top: -10px;
  background-color: #ff8a00;
  z-index: -1;
  opacity: 0;
}

@media (max-width: 480px) {
  .slick-testimonials-thumb:before .slick-testimonials-thumb:before {
    width: 35px;
    height: 35px;
    right: 10px;
    top: -5px;
  }
}

.slick-testimonials-thumb img {
  width: 100%;
  border: 3px solid #fff;
}

.tw-testimonial-border-outer {
  position: absolute;
  width: 100%;
}

@media (max-width: 1199px) {
  .tw-testimonial-border-outer {
    position: relative;
  }
}

@media (max-width: 991px) {
  .tw-testimonial-border-outer {
    margin-bottom: 30px;
  }
}

.tw-testimonial-inner-wrap {
  position: relative;
  z-index: 1;
}

.tw-testimonial-border {
  position: absolute;
  content: '';
  top: 0px;
  left: -12px;
  bottom: 0px;
  width: 50%;
  border-width: 72px 72px 72px 0px;
  border-style: solid;
  border-color: #1e8fd0;
}

@media (max-width: 1199px) {
  .tw-testimonial-border {
    left: auto;
    right: 0px;
  }
}

@media (max-width: 480px) {
  .tw-testimonial-border {
    display: none;
  }
}

.tw-testimonial-border:after {
  position: absolute;
  content: '';
  top: -72px;
  left: -60px;
  width: 60px;
  height: 72px;
  background-color: #1e8fd0;
}

@media (max-width: 480px) {
  .tw-testimonial-border:after {
    display: none;
  }
}

.tw-client-logo-wrap {
  padding: 90px 0px 0px 0px;
  margin-top: 72px;
}

@media (max-width: 1199px) {
  .tw-client-logo-wrap {
    padding: 0px;
    margin-top: 0px;
    margin-bottom: 30px;
  }
}

.tw-client-logo-bg {
  background-color: #f2f2f2;
  position: absolute;
  content: '';
  top: 72px;
  bottom: 200px;
  left: 0px;
  width: 50%;
  z-index: -1;
}

@media (max-width: 1199px) {
  .tw-client-logo-bg {
    display: none;
  }
}

.tw-client-logo-inner {
  position: relative;
  padding-right: 30px;
}

@media (max-width: 991px) {
  .tw-client-logo-inner {
    padding-right: 0px;
  }
}

.tw-client-logo-inner ul {
  list-style: none;
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid #9f9f9f;
  border-left: 1px solid #9f9f9f;
}

.tw-client-logo-inner ul li {
  width: 33.33%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #9f9f9f;
  border-right: 1px solid #9f9f9f;
}

.tw-client-logo-inner ul li .logo-media {
  padding: 30px 0px;
  width: 100%;
  text-align: center;
}

.tw-client-logo-inner ul li .logo-media img {
  width: 100px;
  filter: grayscale(100%);
  transition: 0.5s all ease;
}

.tw-client-logo-inner ul li .logo-media:hover img {
  filter: none;
}

.truck-img {
  position: relative;
}

/*Booking Section*/
.tw-booking-area {
  position: relative;
  background-position: center center;
  background-repeat: no-repeat;
}

.tw-booking-area .tw-booking-media {
  margin-right: -40px;
}

@media (max-width: 991px) {
  .tw-booking-area .tw-booking-media {
    margin-right: 0px;
    margin-bottom: 20px;
    text-align: center;
  }
}

/*  bhoechie tab */
.tw-booking-section {
  padding-bottom: 30px;
}

.tw-booking-form {
  background-color: #fff;
  padding: 50px 30px;
  margin-left: 100px;
  min-height: 540px;
  box-shadow: 0px 0px 60px rgba(30, 143, 208, 0.7);
}

@media (max-width: 991px) {
  .tw-booking-form {
    margin-left: 0px;
  }
}

@media (max-width: 480px) {
  .tw-booking-form {
    padding: 20px;
  }
}

.tw-booking-form .booking-tab-container {
  background-color: #ffffff;
  border-radius: 4px;
}

.tw-booking-form .booking-tab-container .booking-tab-menu {
  padding-right: 0;
  padding-left: 0;
  padding-bottom: 0;
  margin-left: -60px;
  width: 170px;
}

@media (max-width: 1199px) {
  .tw-booking-form .booking-tab-container .booking-tab-menu {
    width: auto;
    margin-left: 30px;
  }
}

@media (max-width: 480px) {
  .tw-booking-form .booking-tab-container .booking-tab-menu {
    margin-left: 10px;
  }
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group {
  margin-bottom: 0;
}

@media (max-width: 1199px) {
  .tw-booking-form .booking-tab-container .booking-tab-menu .list-group {
    display: block;
  }
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group .list-group-item {
  border-radius: 0px;
}

@media (max-width: 1199px) {
  .tw-booking-form .booking-tab-container .booking-tab-menu .list-group .list-group-item {
    display: inline-block;
  }
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group .list-group-item + .list-group-item.active {
  margin-top: 0px;
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a {
  transition: 0.5s all ease;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebebeb;
}

@media (max-width: 480px) {
  .tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a {
    padding: 10px;
  }
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a:last-child {
  margin-bottom: 0px;
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a .media {
  margin-bottom: 10px;
  filter: grayscale(100%);
}

@media (max-width: 480px) {
  .tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a .media img {
    height: 40px;
  }
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a span {
  display: block;
  color: #b7b8b8;
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a.active {
  border: 1px solid transparent;
  box-shadow: 0px 0px 30px rgba(30, 143, 208, 0.5);
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a.active .media {
  margin-bottom: 10px;
  filter: grayscale(0%);
}

.tw-booking-form .booking-tab-container .booking-tab-menu .list-group > a.active span {
  display: block;
  color: #0b2f44;
}

.tw-booking-form .booking-tab-container .booking-tab-content {
  background-color: #ffffff;
  padding-left: 20px;
  padding-top: 10px;
  transition: 0.5s all ease;
}

.tw-booking-form .booking-tab-container .booking-tab-content:not(.active) {
  display: none;
}

@media (max-width: 480px) {
  .tw-booking-form .booking-tab-container .booking-tab-content {
    padding: 0px;
  }
}

.tw-booking-form .tw-booking-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tw-booking-form .tw-booking-footer-text {
  display: inline-table;
  font-family: "Oswald", sans-serif;
  font-size: 120px;
  line-height: 0.75;
  font-weight: 700;
  color: #000;
  text-transform: uppercase;
  opacity: 0.05;
}

@media (max-width: 1199px) {
  .tw-booking-form .tw-booking-footer-text {
    font-size: 50px;
  }
}

@media (max-width: 480px) {
  .tw-booking-form .tw-booking-footer-text {
    display: none;
  }
}

.tw-booking-form .track-and-trace-form textarea.form-control {
  min-height: 100px;
  resize: none;
}

.tw-inline-checked {
  display: flex;
}

@media (max-width: 767px) {
  .tw-inline-checked {
    display: block;
  }
}

.tw-inline-checked .form-check {
  margin-right: 30px;
}

.tw-inline-checked .form-check .form-check-input {
  border-radius: 0px;
}

.book-other-services .form-control,
.book-other-services .form-select {
  border: 0px;
}

@media (max-width: 420px) {
  .book-other-services {
    padding: 15px !important;
  }
}

.form-select {
  height: 50px;
  font-size: 13px;
  line-height: 20px;
  padding: 10px;
  border-radius: 0px;
  color: #6c757d;
}

.form-select option {
  padding-left: 0px;
}

.form-select:focus {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.6);
  background-color: #F8F8F8;
}

/*tw-we-achived*/
@media (max-width: 767px) {
  .tw-we-achived {
    margin: 30px 0px 0px 0px;
  }
}

.tw-we-achived-section {
  display: flex;
}

.tw-we-achived-section .tw-we-achived-box-warp {
  width: 30%;
  display: flex;
}

@media (max-width: 991px) {
  .tw-we-achived-section .tw-we-achived-box-warp {
    display: block;
    width: 100%;
  }
}

.tw-we-achived-section .tw-we-achived-box-warp .tw-we-achived-box {
  padding: 60px 40px 60px 40px;
  display: inline-block;
  margin-left: auto;
}

@media (max-width: 991px) {
  .tw-we-achived-section .tw-we-achived-box-warp .tw-we-achived-box {
    display: block;
    padding: 30px;
  }
}

.tw-we-achived-section .tw-we-achived-box-warp .tw-we-achived-box .counter {
  color: #ff8a00;
  font-size: 60px;
  font-family: "Rubik", sans-serif;
}

@media (max-width: 991px) {
  .tw-we-achived-section .tw-we-achived-box-warp .tw-we-achived-box .counter {
    font-size: 30px;
  }
}

.tw-we-achived-section .tw-we-achived-box-warp .tw-we-achived-box span {
  display: block;
  color: #fff;
  font-size: 14px;
  text-transform: uppercase;
  font-family: "Rubik", sans-serif;
  font-weight: 500;
  letter-spacing: 1px;
  padding-left: 70px;
  position: relative;
}

.tw-we-achived-section .tw-we-achived-box-warp .tw-we-achived-box span:before {
  position: absolute;
  content: '';
  width: 56px;
  height: 10px;
  left: 0px;
  top: 6px;
  background-color: #fff;
}

.tw-we-achived-section .tw-we-achived-box-warp.bg-skew {
  position: relative;
  z-index: 1;
  width: 40%;
}

@media (max-width: 991px) {
  .tw-we-achived-section .tw-we-achived-box-warp.bg-skew {
    width: 100%;
  }
}

.tw-we-achived-section .tw-we-achived-box-warp.bg-skew:after {
  background-color: #fff;
  top: -1px;
  bottom: -1px;
  right: 0px;
  left: 0px;
  position: absolute;
  content: '';
  z-index: -1;
  transform: skewX(-20deg);
}

@media (max-width: 768px) {
  .tw-we-achived-section .tw-we-achived-box-warp.bg-skew:after {
    transform: none;
  }
}

.tw-we-achived-section .tw-we-achived-box-warp.bg-skew span {
  color: #0b2f44;
}

.tw-we-achived-section .tw-we-achived-box-warp.bg-skew span:before {
  background-color: #0b2f44;
}

@media (max-width: 991px) {
  .tw-we-achived-section {
    display: block;
  }
}

/*Estimation Section*/
.tw-estimation-2-section .row {
  justify-content: center;
}

.tw-estimation-2-area {
  position: relative;
}

.tw-estimation-2-area:before {
  height: 440px;
  content: '';
  left: 0px;
  right: 0px;
  bottom: 0px;
  background-color: #f2faff;
  position: absolute;
}

@media (max-width: 991px) {
  .tw-estimation-2-area:before {
    display: none;
  }
}

.tw-est-2-section-block {
  position: relative;
  z-index: 1;
  transition: 0.5s all ease;
  text-align: center;
  margin-top: 62px;
}

@media (max-width: 991px) {
  .tw-est-2-section-block {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .tw-est-2-section-block {
    margin-top: 0px;
    border: 1px solid #f2faff;
  }
}

.tw-est-2-section-block .tw-est-2-section-block-content {
  padding: 80px 30px;
}

@media (max-width: 991px) {
  .tw-est-2-section-block .tw-est-2-section-block-content {
    padding: 80px 30px 30px 30px;
    text-align: center;
  }
}

@media (max-width: 767px) {
  .tw-est-2-section-block .tw-est-2-section-block-content {
    padding: 30px;
  }
}

.tw-est-2-section-block .tw-est-2-section-block-content .tw-est-2-section-number {
  font-size: 160px;
  color: #efefef;
  font-family: "Oswald", sans-serif;
  font-weight: 700;
  line-height: 0.7;
  display: inline-table;
  position: absolute;
  top: -62px;
  left: 30px;
  transition: 0.5s all ease;
}

@media (max-width: 991px) {
  .tw-est-2-section-block .tw-est-2-section-block-content .tw-est-2-section-number {
    left: 50%;
    transform: translateX(-50%);
    font-size: 100px;
    top: -38px;
  }
}

@media (max-width: 767px) {
  .tw-est-2-section-block .tw-est-2-section-block-content .tw-est-2-section-number {
    position: inherit;
    left: auto;
    transform: inherit;
    font-size: 60px;
  }
}

.tw-est-2-section-block .tw-est-2-section-block-content .media {
  width: 170px;
  height: 170px;
  background-color: #f2faff;
  border-radius: 50%;
  line-height: 170px;
  margin: 0px auto 30px;
}

.tw-est-2-section-block .tw-est-2-section-block-content .tw-title {
  color: #0b2f44;
  margin-bottom: 20px;
}

.tw-est-2-section-block .tw-est-2-section-block-content p {
  margin-bottom: 40px;
}

.tw-est-2-section-block:hover, .tw-est-2-section-block.active {
  background-color: #fff;
  box-shadow: 0px 0px 60px rgba(30, 143, 208, 0.5);
}

.tw-est-2-section-block:hover .tw-est-2-section-number, .tw-est-2-section-block.active .tw-est-2-section-number {
  color: #ff8a00;
}

/*Services 3*/
.services-gallery-style3 {
  margin-bottom: 30px;
}

.services-gallery-style3 .service-box-style3 {
  background-color: #fff;
  padding: 30px;
  margin: 20px;
  position: relative;
  z-index: 1;
}

.services-gallery-style3 .service-box-style3:after {
  content: '';
  position: absolute;
  left: -20px;
  top: -20px;
  bottom: -20px;
  right: -20px;
  border: 20px solid #f1f1f1;
  z-index: -1;
  transition: 0.5s all ease;
}

.services-gallery-style3 .service-box-style3:before {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  width: 10px;
  height: 76px;
  background-color: #ff8a00;
  transition: 0.5s all ease;
}

.services-gallery-style3 .service-box-style3 .service-media {
  margin-bottom: 30px;
}

.services-gallery-style3 .service-box-style3 .service-content .service-title-large a {
  transition: all 0.8s linear;
  color: #0b2f44;
}

.services-gallery-style3 .service-box-style3 .service-content .service-title-large-number {
  font-size: 40px;
  color: #ff8a00;
  margin-right: 20px;
  font-weight: 700;
}

.services-gallery-style3 .service-box-style3 .service-content p {
  transition: all 0.8s linear;
  margin-bottom: 20px;
}

.services-gallery-style3 .service-box-style3 .service-content .site-button-2 {
  color: #ff8a00;
  transition: 0.5s all ease;
  position: relative;
  overflow: hidden;
}

.services-gallery-style3 .service-box-style3 .service-content .site-button-2:after {
  content: '\f105';
  position: absolute;
  font-family: 'FontAwesome';
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0px;
  transition: 0.5s all ease;
  opacity: 0;
}

.services-gallery-style3 .service-box-style3 .service-content .site-button-2:hover {
  padding-left: 20px;
}

.services-gallery-style3 .service-box-style3 .service-content .site-button-2:hover:after {
  font-size: 24px;
  left: 0px;
  opacity: 1;
}

.services-gallery-style3 .service-box-style3 .service-media img {
  width: auto;
  height: 60px;
}

.services-gallery-style3 .service-box-style3:hover:after {
  border: 20px solid #ff8a00;
}

.services-gallery-style3 .service-box-style3:hover:before {
  opacity: 0;
}

.tw-sidebar-gallery {
  margin-top: -20px;
}

.tw-sidebar-gallery ul {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  margin: 0px -10px;
}

.tw-sidebar-gallery ul li {
  padding: 20px 10px 0px 10px;
  display: block;
  width: 33.33%;
}

.tw-sidebar-gallery ul li a {
  display: block;
  text-align: center;
  position: relative;
  background-color: #000;
}

.tw-sidebar-gallery ul li a img {
  transition: 0.5s all ease;
}

.tw-sidebar-gallery ul li a i {
  opacity: 0;
  width: 46px;
  height: 46px;
  line-height: 46px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: 0.5s all ease;
}

.tw-sidebar-gallery ul li:hover a img {
  opacity: 0.3;
}

.tw-sidebar-gallery ul li:hover a i {
  opacity: 1;
}

/*Home 1 banner*/
.twm-slider1-wrap {
  background-color: #1e8fd0;
  height: 100vh;
}

.twm-slider1-wrap .swiper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

@media (max-width: 480px) {
  .twm-slider1-wrap .swiper {
    padding-bottom: 50px;
  }
}

.twm-slider1-wrap .swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.twm-slider1-wrap .swiper-pagination-bullet {
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  color: #000;
  opacity: 1;
  background: rgba(0, 0, 0, 0.2);
}

.twm-slider1-wrap .swiper-pagination-bullet-active {
  color: #fff;
  background: #007aff;
}

.twm-slider1-wrap .h-banner-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  max-width: 1140px;
  width: 100%;
}

@media (max-width: 1199px) {
  .twm-slider1-wrap .h-banner-wrap {
    max-width: 991px;
    margin-left: 60px;
  }
}

@media (max-width: 991px) {
  .twm-slider1-wrap .h-banner-wrap {
    margin-top: 100px;
  }
}

@media (max-width: 640px) {
  .twm-slider1-wrap .h-banner-wrap {
    display: block;
    margin-top: 60px;
    margin-left: 15px;
  }
}

@media (max-width: 480px) {
  .twm-slider1-wrap .h-banner-wrap {
    margin-top: 120px;
  }
}

.twm-slider1-wrap .h-banner-wrap .h-banner-left {
  width: 50%;
}

@media (max-width: 640px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-left {
    width: 75%;
    margin-bottom: 30px;
  }
}

@media (max-width: 480px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-left {
    width: calc(100% - 15px);
    margin-bottom: 30px;
  }
}

.twm-slider1-wrap .h-banner-wrap .h-banner-right {
  width: 50%;
  position: relative;
  z-index: 1;
}

@media (max-width: 640px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-right {
    width: 100%;
  }
}

.twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top {
  transition: 0.5s all ease;
}

.twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
  font-size: 120px;
  color: #fff;
  text-transform: uppercase;
  font-family: "Oswald", sans-serif;
  font-weight: 800;
  margin-bottom: 20px;
}

.twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
  display: block;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 2px #fff;
  font-size: 130px;
}

@media (max-width: 1199px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 60px;
  }
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
    font-size: 70px;
  }
}

@media (max-width: 991px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 40px;
  }
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
    font-size: 50px;
  }
}

@media (max-width: 767px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 30px;
  }
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
    font-size: 36px;
  }
}

.twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top p {
  font-size: 24px;
  color: #fff;
  margin-bottom: 40px;
}

@media (max-width: 1199px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top p {
    font-size: 18px;
  }
}

@media (max-width: 767px) {
  .twm-slider1-wrap .h-banner-wrap .h-banner-left .h-bnr-top p {
    margin-bottom: 15px;
    font-size: 16px;
  }
}

.twm-slider1-wrap .h-banner-wrap .h-bnr-btn {
  outline: none;
  color: #fff;
  padding: 12px 30px;
  letter-spacing: 1px;
  position: relative;
  display: inline-table;
  background-color: transparent;
  border: 1px solid #fff;
  font-size: 18px;
  text-transform: uppercase;
  transition: 0.5s all ease;
}

.twm-slider1-wrap .h-banner-wrap .h-bnr-btn:hover {
  background-color: #ff8a00;
  border: 1px solid transparent;
}

@media (max-width: 767px) {
  .twm-slider1-wrap .h-banner-wrap .h-bnr-btn {
    padding: 8px 15px;
    font-size: 14px;
  }
}

.twm-slider1-wrap .h-bnr-r-inner {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 640px) {
  .twm-slider1-wrap .h-bnr-r-inner {
    position: inherit;
    transform: none;
    top: auto;
    right: auto;
  }
}

.twm-slider1-wrap .h-bnr-r-inner .h-bnr-media img {
  position: relative;
  left: 0;
  top: 50px;
}

@media (max-width: 1199px) {
  .twm-slider1-wrap .h-bnr-r-inner .h-bnr-media img {
    left: -38px;
  }
}

@media (max-width: 991px) {
  .twm-slider1-wrap .h-bnr-r-inner .h-bnr-media img {
    left: -58px;
    width: 70%;
  }
}

@media (max-width: 640px) {
  .twm-slider1-wrap .h-bnr-r-inner .h-bnr-media img {
    left: 0px;
  }
}

.twm-slider1-wrap .h-bnr-animation {
  width: 670px;
  height: 680px;
  position: absolute;
  left: 100%;
  top: 0px;
  margin-left: -285px;
  z-index: -1;
}

@media (max-width: 1199px) {
  .twm-slider1-wrap .h-bnr-animation {
    width: 360px;
    height: 580px;
    margin-left: -286px;
  }
}

@media (max-width: 991px) {
  .twm-slider1-wrap .h-bnr-animation {
    width: 360px;
    height: 450px;
    margin-left: -360px;
  }
}

@media (max-width: 768px) {
  .twm-slider1-wrap .h-bnr-animation {
    width: 290px;
    height: 350px;
    margin-left: -290px;
  }
}

@media (max-width: 640px) {
  .twm-slider1-wrap .h-bnr-animation {
    width: 350px;
    height: 460px;
    margin-left: 0px;
    left: auto;
  }
}

@media (max-width: 480px) {
  .twm-slider1-wrap .h-bnr-animation {
    width: calc(100% - 15px);
  }
}

.twm-slider1-wrap .cross-line-box {
  position: absolute;
  z-index: -1;
}

.twm-slider1-wrap .cross-line-box.left {
  left: 10%;
  top: 20%;
}

.twm-slider1-wrap .cross-line-box.right {
  right: 30px;
  bottom: 60px;
  z-index: 1;
}

.twm-slider1-wrap .circle-left-top {
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background-color: #fff;
  opacity: 0.03;
  position: absolute;
  left: -150px;
  top: -200px;
  z-index: -1;
}

.twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets,
.twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal,
.twm-slider1-wrap .swiper-pagination-custom,
.twm-slider1-wrap .swiper-pagination-fraction {
  right: 30px;
  left: auto;
  width: auto;
  top: 50%;
  transform: translateY(-50%);
  bottom: auto;
  border-right: 6px solid rgba(255, 255, 255, 0.5);
  padding-right: 10px;
}

@media (max-width: 480px) {
  .twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets,
  .twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal,
  .twm-slider1-wrap .swiper-pagination-custom,
  .twm-slider1-wrap .swiper-pagination-fraction {
    border: 0px;
    position: inherit;
    right: auto;
    left: auto;
    top: auto;
    width: auto;
    transform: none;
    padding-right: 0px;
    margin-top: 6px;
  }
}

.twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet,
.twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet,
.twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet {
  font-size: 30px;
  line-height: 30px;
  opacity: 0.5;
  background-color: transparent;
  display: block !important;
  color: #fff;
  height: auto;
  width: 60px;
  text-align: center;
  margin-bottom: 15px !important;
  position: relative;
}

@media (max-width: 640px) {
  .twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
  .twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet,
  .twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet,
  .twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet {
    font-size: 16px;
    line-height: 16px;
    width: 30px;
  }
}

@media (max-width: 480px) {
  .twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
  .twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet,
  .twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet,
  .twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet {
    display: inline-block !important;
  }
}

.twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet::before,
.twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet::before,
.twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet::before,
.twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet::before {
  position: absolute;
  left: 0px;
  top: 0px;
  bottom: 0px;
  content: '0';
  font-size: 30px;
  line-height: 30px;
}

@media (max-width: 640px) {
  .twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet::before,
  .twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet::before,
  .twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet::before,
  .twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet::before {
    font-size: 16px;
    line-height: 16px;
  }
}

.twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet:last-child,
.twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet:last-child,
.twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet:last-child,
.twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet:last-child {
  margin-bottom: 0px !important;
}

.twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active,
.twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active,
.twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active,
.twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active {
  font-size: 60px;
  line-height: 60px !important;
  opacity: 1;
  position: relative;
}

@media (max-width: 640px) {
  .twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active,
  .twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active,
  .twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active,
  .twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active {
    font-size: 40px;
    line-height: 40px !important;
    width: 40px;
  }
}

.twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  position: absolute;
  left: -25px;
  top: 0px;
  bottom: 0px;
  content: '0';
  font-size: 60px;
  line-height: 60px !important;
}

@media (max-width: 640px) {
  .twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
  .twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
  .twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
  .twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
    font-size: 40px;
    line-height: 40px !important;
    width: 40px;
  }
}

.twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
.twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
.twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
.twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
  position: absolute;
  right: -20px;
  top: 0px;
  bottom: 0px;
  content: '';
  border-right: 6px solid #fff;
}

@media (max-width: 480px) {
  .twm-slider1-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
  .twm-slider1-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
  .twm-slider1-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
  .twm-slider1-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
    display: none;
  }
}

.slide-top {
  animation: slide-top 2s cubic-bezier(0.55, 0.085, 0.68, 0.53) infinite alternate-reverse both;
}

@keyframes slide-top {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50px);
  }
}

.zoon-in-out {
  animation: zoom-in-zoom-out 8s ease-out infinite;
}

@keyframes zoom-in-zoom-out {
  0% {
    transform: scale(1.1, 1.1);
  }
  50% {
    transform: scale(1.5, 1.5);
  }
  100% {
    transform: scale(1.1, 1.1);
  }
}

.up-down {
  animation: slide-top 1.5s ease-in-out infinite alternate-reverse both;
}

@keyframes slide-top {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-20px);
  }
}

/* Shine Animation*/
.shine-effect .shine-box {
  position: relative;
  overflow: hidden;
}

.shine-effect .shine-box:before {
  position: absolute;
  top: 0;
  left: -90%;
  z-index: 2;
  display: block;
  content: '';
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  transform: skewX(-25deg);
}

.shine-effect .shine-box:hover:before {
  animation: shine .75s;
}

@keyframes shine {
  100% {
    left: 125%;
  }
}

select {
  appearance: none;
  /* Remove default arrow */
  background-image: url(...html);
  /* Add custom arrow */
  padding: 10px 30px 10px 10px;
  background-color: #fff;
}

/*Home Page 2*/
/*Home 1 banner*/
.twm-slider2-wrap {
  background-color: #1e8fd0;
  height: 100vh;
}

.twm-slider2-wrap .swiper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.twm-slider2-wrap .swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  background-position: bottom right;
}

@media (max-width: 991px) {
  .twm-slider2-wrap .swiper-slide {
    background-position: bottom;
  }
}

@media (max-width: 640px) {
  .twm-slider2-wrap .swiper-slide {
    background-position: bottom;
  }
}

.twm-slider2-wrap .swiper-pagination-bullet {
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  color: #000;
  opacity: 1;
  background: rgba(0, 0, 0, 0.2);
}

.twm-slider2-wrap .swiper-pagination-bullet-active {
  color: #fff;
  background: #007aff;
}

.twm-slider2-wrap .h-banner-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  max-width: 1140px;
  width: 100%;
  margin-top: 60px;
}

@media (max-width: 1199px) {
  .twm-slider2-wrap .h-banner-wrap {
    max-width: 991px;
    margin-left: 60px;
  }
}

@media (max-width: 991px) {
  .twm-slider2-wrap .h-banner-wrap {
    margin-top: 100px;
  }
}

@media (max-width: 640px) {
  .twm-slider2-wrap .h-banner-wrap {
    display: block;
    margin: 0px;
  }
}

@media (max-width: 480px) {
  .twm-slider2-wrap .h-banner-wrap {
    margin-top: 60px;
  }
}

.twm-slider2-wrap .h-banner-wrap .h-banner-left {
  width: 50%;
}

@media (max-width: 640px) {
  .twm-slider2-wrap .h-banner-wrap .h-banner-left {
    width: calc(100% - 100px);
    margin: 0px auto;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 30px;
  }
}

@media (max-width: 480px) {
  .twm-slider2-wrap .h-banner-wrap .h-banner-left {
    width: calc(100% - 15px);
    margin-bottom: 30px;
  }
}

.twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top {
  transition: 0.5s all ease;
}

.twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
  font-size: 120px;
  color: #fff;
  text-transform: uppercase;
  font-family: "Oswald", sans-serif;
  font-weight: 800;
  margin-bottom: 20px;
}

.twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
  display: block;
  font-size: 140px;
  color: #ff8a00;
}

@media (max-width: 1199px) {
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 60px;
  }
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
    font-size: 70px;
  }
}

@media (max-width: 767px) {
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 50px;
  }
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
    font-size: 60px;
  }
}

@media (max-width: 640px) {
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 40px;
  }
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 span {
    font-size: 50px;
  }
}

.twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top p {
  font-size: 24px;
  color: #fff;
  margin-bottom: 40px;
}

@media (max-width: 1199px) {
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top p {
    font-size: 18px;
  }
}

@media (max-width: 767px) {
  .twm-slider2-wrap .h-banner-wrap .h-banner-left .h-bnr-top p {
    margin-bottom: 15px;
    font-size: 16px;
  }
}

.twm-slider2-wrap .h-banner-wrap .h-bnr-btn {
  outline: none;
  color: #fff;
  padding: 12px 30px;
  letter-spacing: 1px;
  position: relative;
  display: inline-table;
  background-color: transparent;
  border: 1px solid #fff;
  font-size: 18px;
  text-transform: uppercase;
  transition: 0.5s all ease;
}

.twm-slider2-wrap .h-banner-wrap .h-bnr-btn:hover {
  background-color: #ff8a00;
  border: 1px solid transparent;
}

@media (max-width: 767px) {
  .twm-slider2-wrap .h-banner-wrap .h-bnr-btn {
    padding: 8px 15px;
    font-size: 14px;
  }
}

.twm-slider2-wrap .cross-line-box {
  position: absolute;
  z-index: -1;
}

.twm-slider2-wrap .cross-line-box.left {
  left: 10%;
  top: 20%;
}

.twm-slider2-wrap .cross-line-box.right {
  right: 30px;
  bottom: 60px;
  z-index: 1;
}

.twm-slider2-wrap .circle-left-top {
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background-color: #fff;
  opacity: 0.03;
  position: absolute;
  left: -150px;
  top: -200px;
  z-index: -1;
}

.twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets,
.twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal,
.twm-slider2-wrap .swiper-pagination-custom,
.twm-slider2-wrap .swiper-pagination-fraction {
  left: 50%;
  bottom: 30px;
  width: auto;
  transform: translateX(-50%);
  border-bottom: 6px solid rgba(255, 255, 255, 0.5);
  padding-bottom: 10px;
}

@media (max-width: 420px) {
  .twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets,
  .twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal,
  .twm-slider2-wrap .swiper-pagination-custom,
  .twm-slider2-wrap .swiper-pagination-fraction {
    width: 180px;
  }
}

.twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet,
.twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet,
.twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet {
  font-size: 20px;
  line-height: 20px;
  opacity: 0.5;
  background-color: transparent;
  display: inline-block !important;
  color: #fff;
  height: auto;
  width: 40px;
  text-align: center;
  margin-right: 15px !important;
  position: relative;
}

@media (max-width: 640px) {
  .twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
  .twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet,
  .twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet,
  .twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet {
    font-size: 16px;
    line-height: 16px;
    width: 40px;
  }
}

@media (max-width: 480px) {
  .twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
  .twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet,
  .twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet,
  .twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet {
    display: inline-block !important;
  }
}

.twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet::before,
.twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet::before,
.twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet::before,
.twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet::before {
  position: absolute;
  left: 0px;
  top: 0px;
  bottom: 0px;
  content: '0';
  font-size: 20px;
  line-height: 20px;
}

@media (max-width: 640px) {
  .twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet::before,
  .twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet::before,
  .twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet::before,
  .twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet::before {
    font-size: 16px;
    line-height: 16px;
  }
}

.twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet:last-child,
.twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet:last-child,
.twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet:last-child,
.twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet:last-child {
  margin-right: 0px !important;
}

.twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active,
.twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active,
.twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active,
.twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active {
  font-size: 34px;
  padding-left: 20px;
  line-height: 34px !important;
  opacity: 1;
  position: relative;
}

.twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  position: absolute;
  left: 0px;
  top: 0px;
  bottom: 0px;
  content: '0';
  font-size: 34px;
  line-height: 34px !important;
}

.twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
.twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
.twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
.twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
  position: absolute;
  left: -2px;
  bottom: -18px;
  width: 40px;
  content: '';
  border-top: 6px solid #fff;
}

@media (max-width: 480px) {
  .twm-slider2-wrap .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
  .twm-slider2-wrap .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
  .twm-slider2-wrap .swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active::after,
  .twm-slider2-wrap .swiper-pagination-fraction .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
    display: none;
  }
}

.twm-slider2-wrap .swiper-button-next,
.twm-slider2-wrap .swiper-button-prev {
  color: #fff;
}

.tw-project-2-wrap .tw-project-2-content {
  position: relative;
}

.tw-project-2-wrap .tw-project-2-content .tw-project-2-content-position {
  width: calc(100% - 60px);
  padding: 65px 48px;
  position: relative;
  z-index: 1;
  margin: 0px auto;
}

@media (max-width: 480px) {
  .tw-project-2-wrap .tw-project-2-content .tw-project-2-content-position {
    padding: 15px;
    width: calc(100% - 15px);
  }
}

.tw-project-2-wrap .tw-project-2-content .tw-project-2-content-position:after {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  background-color: #fff;
  z-index: -1;
}

/*Footer dark version css*/
.footer-light {
  color: #0b2f44;
  font-size: 16px;
}

.footer-light .ftr-bg {
  background-color: #efefef;
}

.footer-light .logo-footer {
  margin-bottom: 25px;
  max-width: 174px;
}

.footer-light .footer-top {
  padding: 80px 0px 0px 0px;
}

@media (max-width: 991px) {
  .footer-light .footer-top {
    padding: 30px 0px 0px 0px;
  }
}

.footer-light .widget-title {
  font-family: "Rubik", sans-serif;
  color: #0b2f44;
  font-weight: 400;
  font-size: 20px;
  margin-bottom: 30px;
  padding-bottom: 23px;
  position: relative;
}

.footer-light .widget-title:before {
  content: '';
  position: absolute;
  bottom: 0px;
  left: 0px;
  background-color: #ff8a00;
  width: 60px;
  height: 3px;
}

.footer-light .social-icons {
  margin: 40px 0px 0px 0px;
}

.footer-light .social-icons li {
  display: inline-block;
}

.footer-light .social-icons li a {
  height: 45px;
  width: 45px;
  background-color: #184f6e;
  line-height: 45px;
  padding: 0px;
  color: #fff;
  font-size: 22px;
  text-align: center;
  margin-right: 3px;
}

.footer-light .social-icons li a:hover {
  color: #fff;
  background-color: #051721;
}

.footer-light .footer-bottom {
  padding: 0px;
  color: #fff;
  position: relative;
  z-index: 1;
  font-weight: 400;
}

.footer-light .footer-bottom .footer-bottom-info {
  display: flex;
  padding: 30px 0px;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #0b2f44;
}

@media (max-width: 991px) {
  .footer-light .footer-bottom .footer-bottom-info {
    display: block;
  }
}

@media (max-width: 991px) {
  .footer-light .footer-bottom .footer-copy-right {
    margin: 5px 10px 5px 0px;
    display: inline-block;
  }
}

.footer-light .footer-bottom .footer-copy-right .copyrights-text {
  color: #0b2f44;
  font-size: 14px;
}

.footer-light .widget_services ul li a {
  color: #0b2f44;
  position: relative;
  padding: 0px 0px 5px 0px;
  display: block;
  margin-left: 0px;
  transition: 0.5s all ease;
  font-size: 16px;
}

/*Why Choose Section*/
.tw-why-choose-area-top2 {
  background-position: bottom right;
}

@media (max-width: 767px) {
  .tw-why-choose-area-top2 {
    background-size: auto;
  }
}

.tw-why-choose-area2 {
  padding-bottom: 100px;
}

@media (max-width: 991px) {
  .tw-why-choose-area2 {
    padding-bottom: 40px;
  }
}

.tw-why-choose-section2 .row {
  align-items: center;
}

@media (max-width: 991px) {
  .tw-why-choose-section2 .row {
    padding-bottom: 0px;
  }
}

@media (max-width: 991px) {
  .tw-why-choose-section2 .tw-why-choose-left {
    margin-bottom: 50px;
  }
}

@media (max-width: 480px) {
  .tw-why-choose-section2 .tw-why-choose-left {
    margin-bottom: 40px;
  }
}

.tw-why-choose-section2 .tw-why-choose-left strong {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
  margin-bottom: 20px;
  display: block;
}

.tw-why-choose-section2 .tw-why-choose-left .tw-why-choose-left-bottom {
  padding-top: 30px;
  display: flex;
}

@media (max-width: 480px) {
  .tw-why-choose-section2 .tw-why-choose-left .tw-why-choose-left-bottom {
    display: block;
  }
}

.tw-why-choose-section2 .tw-why-choose-left .tw-why-choose-left-bottom .site-button {
  margin-right: 30px;
}

.tw-why-choose-section2 .tw-why-choose-left .tw-why-choose-left-bottom .sign-font {
  position: relative;
}

.tw-why-choose-section2 .tw-why-choose-left .tw-why-choose-left-bottom .sign-font span {
  font-family: 'Sacramento', cursive;
  font-size: 45px;
  color: #000;
  position: absolute;
  left: 0px;
  top: 0px;
  transform: rotate(-20deg);
}

@media (max-width: 480px) {
  .tw-why-choose-section2 .tw-why-choose-left .tw-why-choose-left-bottom .sign-font span {
    position: relative;
  }
}

.tw-why-choose-section2 .tw-why-choose-right {
  position: relative;
  margin-right: 60px;
  z-index: 1;
}

@media (max-width: 991px) {
  .tw-why-choose-section2 .tw-why-choose-right {
    margin-right: 0px;
    margin-bottom: 50px;
  }
}

.tw-why-choose-section2 .tw-why-choose-right:after {
  content: '';
  width: 470px;
  height: 470px;
  border-radius: 50%;
  background-color: #ff8a00;
  opacity: 1;
  position: absolute;
  left: 50px;
  bottom: 15px;
  z-index: -1;
}

@media (max-width: 1199px) {
  .tw-why-choose-section2 .tw-why-choose-right:after {
    width: 380px;
    height: 380px;
  }
}

@media (max-width: 480px) {
  .tw-why-choose-section2 .tw-why-choose-right:after {
    display: none;
  }
}

.tw-why-choose-section2 .tw-why-choose-right:before {
  content: '';
  width: 500px;
  height: 500px;
  border-radius: 50%;
  border: 2px solid #ff8a00;
  opacity: 1;
  position: absolute;
  left: 50px;
  bottom: 0px;
  z-index: -1;
}

@media (max-width: 1199px) {
  .tw-why-choose-section2 .tw-why-choose-right:before {
    width: 400px;
    height: 400px;
    bottom: 4px;
  }
}

@media (max-width: 480px) {
  .tw-why-choose-section2 .tw-why-choose-right:before {
    display: none;
  }
}

.tw-why-choose-section2 .tw-why-choose-right .tw-why-choose-media1 {
  position: relative;
  max-width: 370px;
  box-shadow: 0px 40px 60px rgba(30, 143, 208, 0.7);
}

@media (max-width: 1199px) {
  .tw-why-choose-section2 .tw-why-choose-right .tw-why-choose-media1 {
    max-width: 300px;
  }
}

.tw-why-choose-section2 .tw-why-choose-right .tw-why-choose-media1 img {
  width: 100%;
}

.tw-why-choose-section2 .tw-why-choose-right .tw-why-choose-tag {
  position: absolute;
  width: 260px;
  padding: 20px 20px 20px 50px;
  right: 0px;
  bottom: 50px;
  z-index: 2;
  background-color: #0b2f44;
  border-right: 6px solid #ff8a00;
}

.tw-why-choose-section2 .tw-why-choose-right .tw-why-choose-tag .tag-box h2 {
  color: #fff;
  font-size: 72px;
}

.tw-why-choose-section2 .tw-why-choose-right .tw-why-choose-tag .tag-box h3 {
  color: #ff8a00;
}

.tw-why-choose-section2 .tw-why-choose-right .tw-why-choose-tag .tag-box span {
  display: block;
  color: #fff;
  position: absolute;
  left: 20px;
  bottom: 7px;
  transform-origin: 0px;
  transform: rotate(-90deg);
}

/*Testimonial 2*/
.tw-testimonial-2-area {
  overflow: hidden;
}

.tw-testimonial-2-area .tw-testimonial-2-area-inner {
  position: relative;
  z-index: 1;
  padding-top: 120px;
}

@media (max-width: 575px) {
  .tw-testimonial-2-area .tw-testimonial-2-area-inner {
    padding-top: 0px;
  }
}

.tw-testimonial-2-area .tw-outer-border {
  position: absolute;
  content: '';
  top: 0px;
  left: 50%;
  bottom: 0px;
  max-width: 1140px;
  width: 100%;
  border: 70px solid #1e8fd0;
  transform: translateX(-50%);
}

@media (max-width: 991px) {
  .tw-testimonial-2-area .tw-outer-border {
    border: 40px solid #1e8fd0;
  }
}

@media (max-width: 575px) {
  .tw-testimonial-2-area .tw-outer-border {
    border: 0px;
  }
}

.tw-testimonial-2-area .slider-testimonial-2-wrap {
  position: relative;
  z-index: 1;
  padding: 0px 80px;
}

@media (max-width: 991px) {
  .tw-testimonial-2-area .slider-testimonial-2-wrap {
    padding: 0px;
  }
}

.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-prev,
.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-next {
  top: auto;
  bottom: -30px;
  width: 30px;
  height: 30px;
}

.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-prev:before,
.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-next:before {
  color: #fff;
  font-family: 'FontAwesome';
  background-color: #0b2f44;
  opacity: 1;
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: block;
}

@media (max-width: 460px) {
  .tw-testimonial-2-area .slider-testimonial-2-wrap .slick-prev,
  .tw-testimonial-2-area .slider-testimonial-2-wrap .slick-next {
    top: 50%;
    transform: translateY(-50%);
  }
}

.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-prev {
  left: 4px;
}

.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-prev:before {
  content: "\f104";
}

.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-next {
  right: 4px;
}

.tw-testimonial-2-area .slider-testimonial-2-wrap .slick-next:before {
  content: "\f105";
}

.tw-testimonial-2-area .slick-testimonials-2-thumb {
  position: relative;
  z-index: 1;
  max-width: 300px;
  left: 50%;
  top: -15px;
  transform: translateX(-50%);
}

.tw-testimonial-2-area .slick-testimonials-2-thumb .slick-testimonials-2-thumbpic {
  cursor: pointer;
  margin-bottom: 15px;
  padding-right: 15px;
  transition: 0.5s all ease;
  position: relative;
  z-index: 1;
}

@media (max-width: 480px) {
  .tw-testimonial-2-area .slick-testimonials-2-thumb .slick-testimonials-2-thumbpic {
    padding: 0px;
  }
}

.tw-testimonial-2-area .slick-testimonials-2-thumb .slick-testimonials-2-thumbpic img {
  width: 100%;
  border: 3px solid #fff;
}

.tw-testimonial-2-area .slick-testimonials-2-thumb .slick-list {
  padding: 0px !important;
}

.tw-testimonial-2-area .slick-testimonials-2-thumb .slick-arrow {
  display: none !important;
}

.tw-testimonial-2-area .slick-testimonials-2-thumb .slick-active.slick-center .slick-testimonials-2-thumbpic img {
  border: 3px solid #ff8a00;
}

@media (max-width: 480px) {
  .tw-testimonial-2-area .slick-testimonials-2-thumb .slick-active.slick-center .slick-testimonials-2-thumbpic img {
    border-color: #ff8a00;
  }
  .tw-testimonial-2-area .slick-testimonials-2-thumb .slick-active.slick-center .slick-testimonials-2-thumbpic:before {
    display: none;
  }
}

.tw-testimonial-2-area .tw-testimonials2-section {
  text-align: center;
  padding: 0px 50px;
  margin-bottom: 80px;
}

@media (max-width: 1270px) {
  .tw-testimonial-2-area .tw-testimonials2-section {
    padding-right: 30px;
  }
}

@media (max-width: 991px) {
  .tw-testimonial-2-area .tw-testimonials2-section {
    padding: 0px 15px;
  }
}

@media (max-width: 480px) {
  .tw-testimonial-2-area .tw-testimonials2-section {
    padding-right: 35px;
    padding-left: 35px;
  }
}

@media (max-width: 460px) {
  .tw-testimonial-2-area .tw-testimonials2-section {
    margin-bottom: 40px;
  }
}

.tw-testimonial-2-area .tw-testimonials2-section .tw-testimonials2-text {
  margin-bottom: 40px;
  position: relative;
  font-size: 14px;
  font-style: italic;
}

@media (max-width: 480px) {
  .tw-testimonial-2-area .tw-testimonials2-section .tw-testimonials2-text {
    margin-bottom: 15px;
  }
}

.tw-testimonial-2-area .tw-testimonials2-section .tw-testimonials2-text .tw-testimonials2-quote {
  display: block;
  margin-bottom: 30px;
  text-align: center;
}

@media (max-width: 480px) {
  .tw-testimonial-2-area .tw-testimonials2-section .tw-testimonials2-text .tw-testimonials2-quote {
    margin-bottom: 15px;
  }
}

.tw-testimonial-2-area .tw-testimonials2-section .tw-testimonials2-text .tw-testimonials2-quote img {
  display: inline-block;
}

.tw-testimonial-2-area .tw-testimonials2-section .tw-testimonials2-name {
  font-size: 30px;
  line-height: 38px;
  font-family: 'Sacramento', cursive;
  color: #0b2f44;
  font-weight: 600;
  text-align: center;
}

.tw-testimonial-2-area .tw-testimonials2-section .tw-testimonials2-postion {
  font-size: 14px;
  color: #ff8a00;
  position: relative;
  text-align: center;
}

/*Home 3 banner*/
.slider-scale {
  transition: all 8s linear;
  transform: translateX(30%) scale(0.8);
}

.slider-scale2 {
  transition: all 8s linear;
  transform: translateX(30%);
}

.twm-slider3-wrap {
  background-color: #1e8fd0;
  height: 100vh;
}

.twm-slider3-wrap .swiper-slide-active .slider-scale {
  transform: translateX(0%) scale(1);
}

.twm-slider3-wrap .swiper-slide-active .slider-scale2 {
  transform: translateX(0%);
}

.twm-slider3-wrap .swiper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.twm-slider3-wrap .swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

@media (max-width: 1365px) {
  .twm-slider3-wrap .swiper-slide {
    padding: 0px 50px;
  }
}

.twm-slider3-wrap .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  color: #000;
  opacity: 1;
  background: #fff;
  transition: 0.5s all ease;
}

.twm-slider3-wrap .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #ff8a00;
}

.twm-slider3-wrap .h-banner-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  max-width: 1230px;
  width: 100%;
}

@media (max-width: 1199px) {
  .twm-slider3-wrap .h-banner-wrap {
    max-width: 991px;
    margin-left: 60px;
  }
}

@media (max-width: 991px) {
  .twm-slider3-wrap .h-banner-wrap {
    margin-top: 100px;
  }
}

@media (max-width: 768px) {
  .twm-slider3-wrap .h-banner-wrap {
    display: block;
    margin-top: 60px;
    margin-left: 0px;
  }
}

@media (max-width: 480px) {
  .twm-slider3-wrap .h-banner-wrap {
    margin-top: 120px;
  }
}

.twm-slider3-wrap .h-banner-wrap .h-banner-left {
  width: 50%;
}

@media (max-width: 768px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left {
    width: 75%;
    margin: 100px auto 30px;
    text-align: center;
  }
}

@media (max-width: 736px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left {
    width: 50%;
    margin: 0px;
    text-align: left;
  }
}

@media (max-width: 640px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left {
    width: 75%;
    margin: 100px auto 30px;
    text-align: center;
  }
}

@media (max-width: 575px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left {
    width: 100%;
  }
}

.twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top {
  transition: 0.5s all ease;
}

.twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top .title-small {
  display: block;
  font-size: 36px;
  font-family: "Oswald", sans-serif;
  color: #fff;
  font-weight: 600;
  margin-bottom: 20px;
}

@media (max-width: 1199px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top .title-small {
    font-size: 32px;
  }
}

@media (max-width: 991px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top .title-small {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top .title-small {
    font-size: 18px;
  }
}

.twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
  font-size: 66px;
  color: #fff;
  text-transform: uppercase;
  font-family: "Oswald", sans-serif;
  font-weight: 800;
  margin-bottom: 20px;
}

@media (max-width: 1365px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 60px;
    margin-bottom: 0px;
  }
}

@media (max-width: 1199px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 45px;
  }
}

@media (max-width: 991px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 40px;
  }
}

@media (max-width: 767px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-left .h-bnr-top h2 {
    font-size: 26px;
  }
}

.twm-slider3-wrap .h-banner-wrap .h-banner-right {
  width: 50%;
  position: absolute;
  right: 0px;
  z-index: 1;
}

@media (max-width: 768px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-right {
    width: 100%;
    position: relative;
  }
}

@media (max-width: 736px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-right {
    width: 50%;
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
  }
}

@media (max-width: 640px) {
  .twm-slider3-wrap .h-banner-wrap .h-banner-right {
    width: 100%;
    position: relative;
    top: auto;
    transform: inherit;
  }
}

.twm-slider3-wrap .h-banner-wrap .h3-bnr-btn {
  margin-top: 40px;
  text-transform: uppercase;
}

.twm-slider3-wrap .h-bnr-r-inner {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 768px) {
  .twm-slider3-wrap .h-bnr-r-inner {
    position: inherit;
    transform: none;
    top: auto;
    right: auto;
  }
}

@media (max-width: 736px) {
  .twm-slider3-wrap .h-bnr-r-inner {
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
  }
}

@media (max-width: 640px) {
  .twm-slider3-wrap .h-bnr-r-inner {
    position: inherit;
    transform: none;
    top: auto;
    right: auto;
  }
}

.twm-slider3-wrap .h-bnr-r-inner .h-bnr-media img {
  position: relative;
  left: 0;
  top: 50px;
}

@media (max-width: 768px) {
  .twm-slider3-wrap .h-bnr-r-inner .h-bnr-media img {
    left: 0px;
  }
}

.twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap {
  position: relative;
  bottom: -100px;
}

@media (max-width: 768px) {
  .twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap {
    bottom: 50px;
    width: 75%;
    margin-left: auto;
  }
}

@media (max-width: 736px) {
  .twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap {
    bottom: 0px;
    width: 100%;
    margin-left: auto;
  }
}

@media (max-width: 640px) {
  .twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap {
    bottom: 50px;
    width: 75%;
    margin-left: auto;
  }
}

.twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap .tyre-1 {
  position: absolute;
  left: 12.9%;
  bottom: 8%;
  width: 12%;
}

.twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap .tyre-2 {
  position: absolute;
  right: 42.8%;
  bottom: 8%;
  width: 12%;
}

.twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap .tyre-3 {
  position: absolute;
  right: 16.8%;
  bottom: 8%;
  width: 12%;
}

.twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap .truck-light {
  position: absolute;
  right: calc(100% - 4%);
  top: 67%;
  width: 400px;
}

@media (max-width: 1199px) {
  .twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap .truck-light {
    width: 347px;
  }
}

@media (max-width: 768px) {
  .twm-slider3-wrap .h-bnr-r-inner .h-bnr-media .trck-animation-wrap .truck-light {
    width: 200px;
  }
}

.twm-slider3-wrap .swiper-button-next,
.twm-slider3-wrap .swiper-button-prev {
  color: #fff;
}

/*truck light blink*/
.blink-image {
  animation: blink normal 2s infinite ease-in-out;
  /* Opera and prob css3 final iteration */
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

/*Slide top fast*/
.slide-top-fast {
  animation: slide-top-fast 1.5s ease-in-out infinite alternate-reverse both;
}

@keyframes slide-top-fast {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-40px);
  }
}

/*Slide top slow*/
.slide-top-slow {
  animation: slide-top-slow 2.5s cubic-bezier(0.55, 0.085, 0.68, 0.53) infinite alternate-reverse both;
}

@keyframes slide-top-slow {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}

/* Tyre Spin */
.spin-tyres {
  animation-name: spin;
  animation-duration: 5000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

/*Cloud Animation*/
.ani-clowd1,
.ani-clowd2,
.ani-clowd3,
.ani-clowd4 {
  position: absolute;
}

.ani-clowd1 img,
.ani-clowd2 img,
.ani-clowd3 img,
.ani-clowd4 img {
  width: 100%;
}

.ani-clowd1 {
  left: 0%;
  top: 0%;
  width: 40%;
}

.ani-clowd2 {
  left: 0%;
  top: 0%;
  width: 40%;
}

.ani-clowd3 {
  left: 0%;
  top: 150px;
  width: 40%;
}

.clowd1-move {
  animation: animateCloud1 25s linear infinite;
}

.clowd2-move {
  animation: animateCloud2 45s linear infinite;
}

.clowd3-move {
  animation: animateCloud3 35s linear infinite;
}

/* animateCloud1 */
@keyframes animateCloud1 {
  0% {
    margin-left: -40%;
  }
  100% {
    margin-left: 100%;
  }
}

/* animateCloud2 */
@keyframes animateCloud2 {
  0% {
    margin-left: -40%;
  }
  100% {
    margin-left: 100%;
  }
}

/* animateCloud3 */
@keyframes animateCloud3 {
  0% {
    margin-left: -40%;
  }
  100% {
    margin-left: 100%;
  }
}

/*What We do 3*/
.tw-what-wedo-area3 {
  overflow: hidden;
}

@media (max-width: 991px) {
  .tw-what-wedo-section3 {
    margin-bottom: 30px;
  }
}

.tw-what-wedo-section3 .tw-what-wedo-media3 {
  position: relative;
  height: 100%;
  z-index: 1;
}

.tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2 {
  position: absolute;
  right: -50px;
  bottom: 0px;
  z-index: 1;
  max-width: inherit;
}

@media (max-width: 1199px) {
  .tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2 {
    max-width: 540px;
    right: auto;
  }
}

@media (max-width: 991px) {
  .tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2 {
    position: relative;
  }
}

@media (max-width: 575px) {
  .tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2 {
    max-width: 440px;
  }
}

@media (max-width: 460px) {
  .tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2 {
    max-width: 300px;
  }
}

.tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2-road {
  position: absolute;
  left: 50px;
  bottom: 0px;
  z-index: 0;
  max-width: inherit;
}

@media (max-width: 991px) {
  .tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2-road {
    left: 0;
  }
}

@media (max-width: 575px) {
  .tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2-road {
    max-width: 85%;
  }
}

@media (max-width: 460px) {
  .tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2-road {
    max-width: 75%;
  }
}

.tw-what-wedo-section3 .tw-what-wedo-media3 .truck-2-bg {
  background-color: #ff8a00;
  width: 3500px;
  position: absolute;
  left: 90px;
  top: 0px;
  bottom: 80px;
  z-index: -1;
}

.tw-service-icon-box-wrap3 {
  position: relative;
}

.service-icon-box-three {
  position: relative;
  margin-bottom: 5px;
  padding: 20px;
  background-color: #fff;
  transition: all 0.3s linear;
  z-index: 0;
}

.service-icon-box-three:before {
  width: 8px;
  content: '';
  position: absolute;
  z-index: 1;
  opacity: 0.5;
  left: -8px;
  top: 30px;
  bottom: 30px;
  background-color: #ff8a00;
  transition: all 0.3s linear;
}

.service-icon-box-three:hover {
  box-shadow: 0px 0px 50px rgba(30, 143, 208, 0.5);
  z-index: 999;
}

.service-icon-box-three:hover:before {
  left: 0%;
  opacity: 1;
}

.service-icon-box-three .service-icon-box-three-media {
  margin-right: 30px;
  margin-top: 20px;
  float: left;
  width: 70px;
}

@media (max-width: 400px) {
  .service-icon-box-three .service-icon-box-three-media {
    margin-right: 20px;
    width: 40px;
  }
}

.service-icon-box-three .service-icon-box-title {
  overflow: hidden;
}

.service-icon-box-three .service-icon-box-title .wt-title {
  margin-bottom: 15px;
}

.service-icon-box-three .service-icon-box-title .wt-title span {
  font-size: 32px;
  padding-right: 5px;
}

@media (max-width: 767px) {
  .service-icon-box-three .service-icon-box-title .wt-title {
    padding-right: 0px;
  }
}

.service-icon-box-three .service-icon-box-title p {
  margin-bottom: 0px;
}

.service-icon-box-three.site-bg-black .service-icon-box-title .wt-title a {
  color: #fff;
}

/*Why choose us*/
.tw-why-choose-area3-top {
  background-position: bottom right;
}

@media (max-width: 767px) {
  .tw-why-choose-area3-top {
    background-size: auto;
  }
}

.tw-why-choose3-section .row {
  align-items: center;
}

@media (max-width: 991px) {
  .tw-why-choose3-section .row {
    padding-bottom: 0px;
  }
}

@media (max-width: 991px) {
  .tw-why-choose3-section .tw-why-choose-left {
    margin-bottom: 50px;
  }
}

@media (max-width: 480px) {
  .tw-why-choose3-section .tw-why-choose-left {
    margin-bottom: 40px;
  }
}

.tw-why-choose3-section .tw-why-choose-left strong {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
  margin-bottom: 20px;
  display: block;
}

.tw-why-choose3-section .tw-why-choose-left .tw-why-choose-left-bottom {
  padding-top: 30px;
  display: flex;
}

@media (max-width: 480px) {
  .tw-why-choose3-section .tw-why-choose-left .tw-why-choose-left-bottom {
    display: block;
  }
}

.tw-why-choose3-section .tw-why-choose-left .tw-why-choose-left-bottom .site-button {
  margin-right: 30px;
}

.tw-why-choose3-section .tw-why-choose-left .tw-why-choose-left-bottom .sign-font {
  position: relative;
}

.tw-why-choose3-section .tw-why-choose-left .tw-why-choose-left-bottom .sign-font span {
  font-family: 'Sacramento', cursive;
  font-size: 45px;
  color: #000;
  position: absolute;
  left: 0px;
  top: 0px;
  transform: rotate(-20deg);
}

@media (max-width: 480px) {
  .tw-why-choose3-section .tw-why-choose-left .tw-why-choose-left-bottom .sign-font span {
    position: relative;
  }
}

.tw-why-choose3-section .tw-why-choose-right {
  position: relative;
}

@media (max-width: 991px) {
  .tw-why-choose3-section .tw-why-choose-right {
    margin-bottom: 30px;
  }
}

.tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media1 {
  position: relative;
  z-index: 1;
  max-width: 358px;
  padding-top: 160px;
  margin-left: auto;
  padding-right: 20px;
}

@media (max-width: 480px) {
  .tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media1 {
    padding-top: 0px;
    max-width: 100%;
    margin-bottom: 40px;
    padding-right: 0px;
  }
}

.tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media1 img {
  width: 100%;
}

.tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media2 {
  position: absolute;
  max-width: 338px;
  left: 0px;
  top: 0px;
  z-index: 2;
}

@media (max-width: 480px) {
  .tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media2 {
    position: inherit;
    max-width: 100%;
    margin-left: 0px;
    margin-bottom: 30px;
  }
  .tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media2 img {
    width: 100%;
  }
}

.tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media2:after {
  position: absolute;
  content: '';
  border-right: 40px solid #fff;
  border-bottom: 40px solid #fff;
  right: -40px;
  bottom: -40px;
  height: 100%;
  width: 100%;
  z-index: -1;
  box-shadow: 0px 40px 60px rgba(30, 143, 208, 0.7);
}

@media (max-width: 480px) {
  .tw-why-choose3-section .tw-why-choose-right .tw-why-choose-media2:after {
    border-right: 20px solid #fff;
    border-bottom: 20px solid #fff;
    right: -20px;
    bottom: -20px;
  }
}

/*Booking area 3*/
.tw-booking-area-bg-wrap {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.tw-booking-area-bg-wrap .tw-booking-area-bg {
  background-position: left bottom 100px;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  position: absolute;
  left: -200px;
  top: 0px;
  z-index: -1;
}

@media (max-width: 1199px) {
  .tw-booking-area-bg-wrap .tw-booking-area-bg {
    left: -322px;
    top: -70px;
  }
}

@media (max-width: 991px) {
  .tw-booking-area-bg-wrap .tw-booking-area-bg {
    display: none;
  }
}

@media (max-width: 991px) {
  .tw-estimation-area3 .row [class*='col-']:last-child .tw-est-section-block3 {
    margin-top: 0px;
  }
}

@media (max-width: 767px) {
  .tw-estimation-area3 .row {
    padding: 10px 0px;
  }
  .tw-estimation-area3 .row [class*='col-']:last-child .tw-est-section-block3 {
    margin-top: 15px;
  }
}

/*Estimation section 3*/
.tw-est-section-block3 {
  height: 550px;
  position: relative;
  z-index: 1;
  border: 15px solid transparent;
  background-color: rgba(0, 0, 0, 0.4);
  text-align: center;
  transition: 0.5s all ease;
}

@media (max-width: 991px) {
  .tw-est-section-block3 {
    height: auto;
    margin-top: 25px;
    margin-bottom: 25px;
  }
}

@media (max-width: 767px) {
  .tw-est-section-block3 {
    height: auto;
    margin-top: 15px;
    margin-bottom: 15px;
  }
}

.tw-est-section-block3 .tw-est-section-block-content {
  position: absolute;
  left: 0px;
  bottom: 0px;
  color: #fff;
  padding: 10px;
  transform: translate3d(0%, 0, 0);
  transition: opacity 0.35s, transform 0.8s;
}

@media (max-width: 991px) {
  .tw-est-section-block3 .tw-est-section-block-content {
    position: relative;
  }
}

.tw-est-section-block3 .tw-est-section-block-content .tw-est-section-number {
  font-size: 80px;
  font-family: "Oswald", sans-serif;
  font-weight: 700;
}

@media (max-width: 991px) {
  .tw-est-section-block3 .tw-est-section-block-content .tw-est-section-number {
    font-size: 40px;
  }
}

.tw-est-section-block3 .tw-est-section-block-content .tw-title {
  color: #fff;
}

.tw-est-section-block3:hover {
  border: 15px solid rgba(255, 255, 255, 0.5);
  background-color: #fff;
}

.tw-est-section-block3:hover .tw-est-section-block-content {
  transform: translate3d(0%, -100px, 0);
  color: #ff8a00;
}

@media (max-width: 991px) {
  .tw-est-section-block3:hover .tw-est-section-block-content {
    transform: translate3d(0%, -20px, 0);
  }
}

.tw-est-section-block3:hover .tw-est-section-block-content .tw-title {
  color: #0b2f44;
}

.tw-est-section-block3:hover .tw-est-section-block-content p {
  color: #0b2f44;
}

.tw-est-section-block3:hover .tw-est-section-block-content .site-button-2-outline {
  border: 2px solid #0b2f44;
}

.tw-est-section-block3:hover .tw-est-section-block-content .site-button-2-outline i {
  color: #0b2f44;
}

.tw-est-section-block3:hover .tw-est-section-block-content .site-button-2-outline i:after {
  background-color: #0b2f44;
}

/*Testimonial 3*/
.tw-testimonial-3-area {
  overflow: hidden;
}

.tw-testimonial-3-area .tw-testimonial-3-area-inner {
  position: relative;
  z-index: 1;
  padding-top: 125px;
  padding-bottom: 125px;
}

@media (max-width: 575px) {
  .tw-testimonial-3-area .tw-testimonial-3-area-inner {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

.tw-testimonial-3-area .tw-outer-border {
  position: absolute;
  content: '';
  top: 0px;
  left: 50%;
  bottom: 0px;
  max-width: 100%;
  width: 100%;
  border: 70px solid #1e8fd0;
  transform: translateX(-50%);
}

@media (max-width: 991px) {
  .tw-testimonial-3-area .tw-outer-border {
    border: 40px solid #1e8fd0;
  }
}

@media (max-width: 575px) {
  .tw-testimonial-3-area .tw-outer-border {
    border: 0px;
  }
}

.tw-testimonial-3-area .slider-testimonial-3-wrap {
  position: relative;
  z-index: 1;
  padding: 0px 80px;
}

@media (max-width: 991px) {
  .tw-testimonial-3-area .slider-testimonial-3-wrap {
    padding: 0px 50px;
  }
}

@media (max-width: 575px) {
  .tw-testimonial-3-area .slider-testimonial-3-wrap {
    padding: 0px;
  }
}

.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-prev,
.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-next {
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
}

.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-prev:before,
.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-next:before {
  color: #fff;
  font-family: 'FontAwesome';
  background-color: #0b2f44;
  opacity: 1;
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: block;
}

@media (max-width: 460px) {
  .tw-testimonial-3-area .slider-testimonial-3-wrap .slick-prev,
  .tw-testimonial-3-area .slider-testimonial-3-wrap .slick-next {
    top: 50%;
    transform: translateY(-50%);
  }
}

.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-prev {
  left: 4px;
}

.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-prev:before {
  content: "\f104";
}

.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-next {
  right: 4px;
}

.tw-testimonial-3-area .slider-testimonial-3-wrap .slick-next:before {
  content: "\f105";
}

.tw-testimonial-3-area .slick-testimonials-3-thumb {
  position: relative;
  z-index: 1;
  max-width: 300px;
  left: 50%;
  top: -15px;
  transform: translateX(-50%);
}

.tw-testimonial-3-area .slick-testimonials-3-thumb .slick-testimonials-3-thumbpic {
  cursor: pointer;
  margin-bottom: 15px;
  padding-right: 15px;
  transition: 0.5s all ease;
  position: relative;
  z-index: 1;
}

@media (max-width: 480px) {
  .tw-testimonial-3-area .slick-testimonials-3-thumb .slick-testimonials-3-thumbpic {
    padding: 0px;
  }
}

.tw-testimonial-3-area .slick-testimonials-3-thumb .slick-testimonials-3-thumbpic img {
  width: 100%;
  border: 3px solid #fff;
}

.tw-testimonial-3-area .slick-testimonials-3-thumb .slick-list {
  padding: 0px !important;
}

.tw-testimonial-3-area .slick-testimonials-3-thumb .slick-arrow {
  display: none !important;
}

.tw-testimonial-3-area .slick-testimonials-3-thumb .slick-active.slick-center .slick-testimonials-3-thumbpic img {
  border: 3px solid #ff8a00;
}

@media (max-width: 480px) {
  .tw-testimonial-3-area .slick-testimonials-3-thumb .slick-active.slick-center .slick-testimonials-3-thumbpic img {
    border-color: #ff8a00;
  }
  .tw-testimonial-3-area .slick-testimonials-3-thumb .slick-active.slick-center .slick-testimonials-3-thumbpic:before {
    display: none;
  }
}

.tw-testimonial-3-area .tw-testimonials3-section {
  text-align: center;
  padding: 0px 50px;
  margin-bottom: 80px;
}

@media (max-width: 1270px) {
  .tw-testimonial-3-area .tw-testimonials3-section {
    padding-right: 30px;
  }
}

@media (max-width: 991px) {
  .tw-testimonial-3-area .tw-testimonials3-section {
    padding: 0px 15px;
  }
}

@media (max-width: 575px) {
  .tw-testimonial-3-area .tw-testimonials3-section {
    margin-bottom: 40px;
  }
}

.tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-text {
  margin-bottom: 40px;
  position: relative;
  font-size: 14px;
  font-style: italic;
}

@media (max-width: 575px) {
  .tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-text {
    padding: 0px 35px;
  }
}

@media (max-width: 480px) {
  .tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-text {
    margin-bottom: 15px;
    padding: 0px 15px;
  }
}

.tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-text .tw-testimonials3-quote {
  display: block;
  margin-bottom: 30px;
  text-align: center;
}

@media (max-width: 480px) {
  .tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-text .tw-testimonials3-quote {
    margin-bottom: 15px;
  }
}

.tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-text .tw-testimonials3-quote img {
  display: inline-block;
}

.tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-name {
  font-size: 30px;
  line-height: 38px;
  font-family: 'Sacramento', cursive;
  color: #0b2f44;
  font-weight: 600;
  text-align: center;
}

.tw-testimonial-3-area .tw-testimonials3-section .tw-testimonials3-postion {
  font-size: 14px;
  color: #ff8a00;
  position: relative;
  text-align: center;
}

/*Client carousel 2*/
.home-client3-outer {
  position: relative;
  top: -140px;
  margin-bottom: -140px;
}

@media (max-width: 991px) {
  .home-client3-outer {
    margin-bottom: -110px;
  }
}

@media (max-width: 575px) {
  .home-client3-outer {
    top: 0px;
    margin-bottom: 40px;
  }
}

.home-client3-outer .home-client-carousel3 {
  padding: 50px;
  background-color: #fff;
  box-shadow: 0 1px 30px 0 rgba(0, 0, 0, 0.1);
}

.home-client3-outer .home-client-carousel3 .client-logo {
  max-width: 130px;
  margin-left: auto;
  margin-right: auto;
}

.home-client3-outer .home-client-carousel3 .client-logo a {
  height: 100px;
  display: flex;
  justify-content: space-around;
}

.home-client3-outer .home-client-carousel3 .client-logo a img {
  width: auto;
  height: auto;
  filter: grayscale(100%);
  transition: 0.5s all ease;
}

.home-client3-outer .home-client-carousel3 .client-logo a:hover img {
  filter: none;
}

/*Services 3*/
.services-gallery-style4 {
  margin-bottom: 30px;
}

@media (max-width: 1200px) {
  .services-gallery-style4 {
    padding-left: 0px;
  }
}

@media (max-width: 991px) {
  .services-gallery-style4 {
    margin-bottom: 30px;
  }
}

.services-gallery-style4 .owl-carousel .owl-stage-outer {
  position: relative;
  margin-bottom: 30px;
}

.services-gallery-style4 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-next {
  right: -1px;
  transition: all 0.2s linear;
}

.services-gallery-style4 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-prev {
  transition: all 0.2s linear;
}

.services-gallery-style4 .services-gallery-one.owl-btn-bottom-left .owl-nav {
  opacity: 1;
  position: absolute;
  left: 0px;
  bottom: 0px;
}

@media (max-width: 991px) {
  .services-gallery-style4 .services-gallery-one.owl-btn-bottom-left .owl-nav {
    text-align: center;
    position: inherit;
    margin-top: 20px;
    bottom: 20px;
  }
  .services-gallery-style4 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-prev {
    margin-right: 14px;
  }
  .services-gallery-style4 .services-gallery-one.owl-btn-bottom-left .owl-nav .owl-next {
    margin-right: 0px;
  }
}

.service-box-style4 {
  position: relative;
  overflow: hidden;
}

.service-box-style4 .service-content {
  width: 100%;
}

.service-box-style4 .service-content .service-content-inner {
  padding: 30px;
  margin: 20px;
  position: relative;
  z-index: 1;
}

@media (max-width: 767px) {
  .service-box-style4 .service-content .service-content-inner {
    margin-left: 0px;
  }
}

.service-box-style4 .service-content .service-content-inner:after {
  content: '';
  border: 2px solid #ff8a00;
  position: absolute;
  top: -20px;
  right: -20px;
  bottom: 20px;
  left: 20px;
  z-index: -1;
}

.service-box-style4 .service-content .service-content-inner:before {
  content: '';
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  background-color: #fff;
  transition: all 0.2s linear;
}

.service-box-style4 .service-content .service-content-inner .service-content-top {
  position: relative;
}

.service-box-style4 .service-content .service-content-inner .service-content-top .service-title-large {
  text-align: right;
}

.service-box-style4 .service-content .service-content-inner .service-content-top .service-title-large a {
  color: #0b2f44;
  transition: all 0.2s linear;
  font-size: 32px;
  line-height: 0.7;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .service-box-style4 .service-content .service-content-inner .service-content-top .service-title-large a {
    font-size: 26px;
  }
}

.service-box-style4 .service-content .service-content-inner .service-content-bottom {
  position: relative;
}

.service-box-style4 .service-content .service-content-inner .service-content-bottom .service-title-large-number {
  font-size: 40px;
  color: #ff8a00;
  transition: all 0.2s linear;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .service-box-style4 .service-content .service-content-inner .service-content-bottom .service-title-large-number {
    font-size: 30px;
  }
}

.service-box-style4 .service-content .service-content-inner .service-content-bottom p {
  transition: all 0.2s linear;
  margin-bottom: 20px;
}

.service-box-style4 .service-content .service-content-inner .service-content-bottom .site-button-2 {
  color: #0b2f44;
  transition: all 0.2s linear;
}

.service-box-style4:hover .service-content-inner:before {
  background-color: #ff8a00;
}

.service-box-style4:hover .service-content-inner .service-content-top .service-title-large a {
  color: #fff;
}

.service-box-style4:hover .service-content-inner .service-content-bottom .service-title-large-number {
  color: #fff;
}

.service-box-style4:hover .service-content-inner .service-content-bottom p {
  color: #fff;
}

.service-box-style4:hover .service-content-inner .service-content-bottom .site-button-2 {
  color: #fff;
}

/*Company approch*/
.tw-company-approch-section3 {
  position: relative;
  margin-top: 100px;
  margin-bottom: 90px;
}

@media (max-width: 991px) {
  .tw-company-approch-section3 {
    margin-top: 30px;
    margin-bottom: 50px;
  }
}

@media (max-width: 768px) {
  .tw-company-approch-section3 {
    margin-bottom: 30px;
  }
}

.tw-company-approch-section3 .tw-company-approch-inner {
  position: relative;
  z-index: 1;
}

@media (max-width: 575px) {
  .tw-company-approch-section3 .tw-company-approch-inner {
    margin-left: 0px;
  }
}

@media (max-width: 991px) {
  .tw-company-approch-section3 .tw-company-approch-inner .row [class*='col-']:last-child .counter-outer-two {
    margin-bottom: 0px;
  }
}

/*Counter*/
.counter-outer-three-wrap {
  background-color: #fff;
  padding: 30px;
  box-shadow: 0px 0px 50px rgba(30, 143, 208, 0.5);
  position: relative;
}

@media (max-width: 991px) {
  .counter-outer-three-wrap {
    margin-bottom: 60px;
  }
}

@media (max-width: 575px) {
  .counter-outer-three-wrap {
    margin-bottom: 20px;
  }
}

.counter-outer-three-wrap:before {
  position: absolute;
  content: '';
  top: -60px;
  bottom: -60px;
  right: 0px;
  border-style: solid;
  border-color: #ff8a00;
  border-width: 60px 60px 60px 0px;
  width: 300px;
  z-index: 0;
}

@media (max-width: 575px) {
  .counter-outer-three-wrap:before {
    top: -20px;
    bottom: -20px;
    right: 0px;
    border-width: 20px 20px 20px 0px;
    width: 200px;
  }
}

.counter-outer-three-wrap .counter-outer-three {
  position: relative;
  margin-bottom: 40px;
}

.counter-outer-three-wrap .counter-outer-three .tw-counter-media {
  position: absolute;
  bottom: 0px;
  left: 0px;
}

.counter-outer-three-wrap .counter-outer-three .tw-counter-media img {
  height: 70px;
}

@media (max-width: 991px) {
  .counter-outer-three-wrap .counter-outer-three .tw-counter-media img {
    height: 46px;
  }
}

@media (max-width: 991px) {
  .counter-outer-three-wrap .counter-outer-three .tw-counter-media img {
    height: 46px;
  }
}

.counter-outer-three-wrap .counter-outer-three .tw-count-number {
  font-size: 28px;
  line-height: 28px;
  margin-bottom: 15px;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .counter-outer-three-wrap .counter-outer-three .tw-count-number {
    font-size: 30px;
    line-height: 30px;
  }
}

.counter-outer-three-wrap .counter-outer-three .counter {
  font-size: 46px;
  line-height: 50px;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .counter-outer-three-wrap .counter-outer-three .counter {
    font-size: 30px;
    line-height: 30px;
  }
}

@media (max-width: 768px) {
  .counter-outer-three-wrap .counter-outer-three .counter {
    font-size: 24px;
  }
}

.counter-outer-three-wrap .counter-outer-three .icon-content {
  overflow: hidden;
  padding-left: 100px;
}

@media (max-width: 991px) {
  .counter-outer-three-wrap .counter-outer-three .icon-content {
    padding-left: 80px;
  }
}

.counter-outer-three-wrap .counter-outer-three .icon-content-info {
  font-weight: 600;
  color: #0b2f44;
  margin-bottom: 0px;
}

.counter-outer-three-wrap .counter-outer-three:last-child {
  margin-bottom: 0px;
}

/*Text with bg image*/
.tw-company-years3 {
  background-color: #fff;
}

@media (max-width: 991px) {
  .tw-company-years3 {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
  }
}

.tw-company-years3 .light {
  position: relative;
  margin-right: 60px;
  display: inline-flex;
}

.tw-company-years3 .light h1 {
  font-size: 350px;
  line-height: 280px;
  display: block;
  color: #0b2f44;
  margin-bottom: 40px;
}

@media (max-width: 991px) {
  .tw-company-years3 .light h1 {
    font-size: 250px;
    line-height: 200px;
  }
}

@media (max-width: 575px) {
  .tw-company-years3 .light h1 {
    font-size: 100px;
    line-height: 100px;
    margin-bottom: 0px;
  }
}

@media (max-width: 991px) {
  .tw-company-years3 .light {
    margin-right: 20px;
  }
}

.tw-company-years3 img {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  mix-blend-mode: lighten;
  transform: translateY(-50%);
  margin-top: -5px;
}

.tw-company-years3 .tw-company-info {
  max-width: 500px;
}

.tw-company-years3 .tw-company-info span {
  font-size: 36px;
  line-height: 36px;
  font-weight: 600;
  font-family: "Oswald", sans-serif;
  color: #0b2f44;
}

@media (max-width: 991px) {
  .tw-company-years3 .tw-company-info span {
    font-size: 30px;
  }
}

@media (max-width: 575px) {
  .tw-company-years3 .tw-company-info span {
    font-size: 26px;
  }
}

@media (max-width: 400px) {
  .tw-company-years3 .tw-company-info span {
    font-size: 20px;
    line-height: 20px;
  }
}

/*Faq*/
.tw-faq-section {
  max-width: 950px;
  margin: 0px auto;
}

.tw-faq-section .tw-faq .accordion-item {
  border: 1px solid transparent;
  margin-bottom: 20px;
  overflow: hidden;
}

.tw-faq-section .tw-faq .accordion-item .accordion-button {
  font-size: 20px;
  color: #0b2f44;
  padding: 10px 16px 10px 16px;
  border: 1px solid #efefef;
}

@media (max-width: 767px) {
  .tw-faq-section .tw-faq .accordion-item .accordion-button {
    font-size: 16px;
  }
}

.tw-faq-section .tw-faq .accordion-item .accordion-button:not(.collapsed) {
  box-shadow: none;
}

.tw-faq-section .tw-faq .accordion-item .accordion-button:focus {
  box-shadow: none !important;
}

.accordion-button:after {
  background-image: none;
  content: "\f067";
  font-family: 'FontAwesome';
  width: 40px;
  height: 40px;
  line-height: 40px;
  color: #ff8a00;
  text-align: center;
}

.accordion-button:not(.collapsed):after {
  background-image: none;
  content: "\f068";
  transform: none;
}

/*Error 404*/
/*-----Error Full Page-----*/
.error-full-page {
  display: flex;
  align-items: center;
  min-height: 100vh;
}

.error-full-page .error-full-page-inner {
  display: table-cell;
  text-align: center;
  margin: auto;
  vertical-align: middle;
  position: relative;
}

.error-full-page .error-full-page-inner-info .row {
  display: flex;
  align-items: center;
}

.error-full-page .error-full-page-inner-info .row p {
  color: #fff;
  margin-bottom: 40px;
}

.error-full-page .error-full-page-inner-info .row h3 {
  color: #fff;
  font-size: 36px;
}

.error-full-page .error-full-page-inner-info .row h4 {
  color: #fff;
  font-size: 50px;
  margin-bottom: 30px;
}

@media (max-width: 991px) {
  .error-full-page .error-full-page-inner-info .row h4 {
    font-size: 36px;
  }
}

.error-full-page .error-full-page-inner-info .row strong {
  font-size: 150px;
  line-height: 150px;
  padding-bottom: 20px;
  display: block;
  font-weight: 900;
  color: #ff8a00;
  font-family: "Oswald", sans-serif;
}

@media (max-width: 991px) {
  .error-full-page .error-full-page-inner-info .row strong {
    font-size: 90px;
    line-height: 90px;
  }
}

@media (max-width: 400px) {
  .error-full-page .error-full-page-inner-info .row strong {
    font-size: 60px;
    line-height: 60px;
  }
}
/*# sourceMappingURL=style.css.map */